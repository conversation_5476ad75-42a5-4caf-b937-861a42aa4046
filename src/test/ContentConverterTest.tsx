/**
 * 内容转换器测试组件
 * 用于验证新的 TipTap 原生转换器功能
 */

import React, { useState, useCallback, useEffect } from "react";
import {
  Card,
  Button,
  Input,
  Alert,
  Tabs,
  Space,
  Typography,
  Row,
  Col,
} from "antd";
import {
  PlayCircleOutlined,
  ClearOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import WysiwygEditor from "../components/notes/WysiwygEditor";
import { ContentConverter } from "../utils/contentConverter";
import { contentAdapter } from "../utils/contentAdapter";
import type { JSONContent } from "@tiptap/react";

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 测试用例接口
 */
interface TestCase {
  name: string;
  description: string;
  markdown: string;
  expectedFeatures: string[];
}

/**
 * 预定义测试用例
 */
const TEST_CASES: TestCase[] = [
  {
    name: "基础任务列表",
    description: "测试基本的任务列表转换",
    markdown: `# 任务列表测试

- [ ] 未完成任务
- [x] 已完成任务
- [ ] 另一个未完成任务

普通文本段落。`,
    expectedFeatures: ["任务列表", "标题", "段落"],
  },
  {
    name: "嵌套任务列表",
    description: "测试嵌套的任务列表结构",
    markdown: `# 项目计划

- [ ] 需求分析
  - [x] 用户调研
  - [ ] 功能定义
- [ ] 技术实现
  - [ ] 前端开发
  - [ ] 后端开发
  - [x] 数据库设计`,
    expectedFeatures: ["嵌套任务列表", "缩进"],
  },
  {
    name: "混合内容",
    description: "测试任务列表与其他内容的混合",
    markdown: `# 学习笔记

## 今日任务
- [x] 阅读文档
- [ ] 写代码
- [ ] 测试功能

## 重要提醒
这是一个**重要**的提醒，需要注意以下几点：

1. 保持代码整洁
2. 及时提交
3. 写好注释

### 任务清单
- [ ] 完成第一阶段
- [x] 准备演示材料`,
    expectedFeatures: ["多级标题", "任务列表", "有序列表", "加粗文本"],
  },
  {
    name: "复杂表格",
    description: "测试表格的转换（如果支持）",
    markdown: `# 数据统计

| 项目 | 状态 | 进度 |
|------|------|------|
| 前端 | ✅ 完成 | 100% |
| 后端 | 🚧 进行中 | 75% |
| 测试 | ⏳ 待开始 | 0% |

## 总结
- [x] 制定计划
- [ ] 执行计划
- [ ] 完成验收`,
    expectedFeatures: ["表格", "任务列表", "表情符号"],
  },
];

export const ContentConverterTest: React.FC = () => {
  const [converter] = useState(() => new ContentConverter());
  const [selectedTest, setSelectedTest] = useState<TestCase>(TEST_CASES[0]);
  const [customMarkdown, setCustomMarkdown] = useState("");
  const [testInput, setTestInput] = useState("");
  const [conversionResults, setConversionResults] = useState<{
    markdownToJSON?: any;
    jsonToMarkdown?: any;
    markdownToHTML?: any;
    htmlToMarkdown?: any;
    editorContent?: string;
  }>({});
  const [testStatus, setTestStatus] = useState<
    "idle" | "running" | "completed"
  >("idle");

  // 初始化测试输入
  useEffect(() => {
    setTestInput(selectedTest.markdown);
  }, [selectedTest]);

  // 运行转换测试
  const runConversionTest = useCallback(async () => {
    setTestStatus("running");

    try {
      const input = testInput || customMarkdown;

      // 1. Markdown → JSON
      const markdownToJSONResult = converter.markdownToJSON(input);

      // 2. JSON → Markdown
      let jsonToMarkdownResult;
      if (markdownToJSONResult.success) {
        jsonToMarkdownResult = converter.jsonToMarkdown(
          markdownToJSONResult.content as JSONContent
        );
      }

      // 3. Markdown → HTML
      const markdownToHTMLResult = converter.markdownToHTML(input);

      // 4. HTML → Markdown
      let htmlToMarkdownResult;
      if (markdownToHTMLResult.success) {
        htmlToMarkdownResult = converter.htmlToMarkdown(
          markdownToHTMLResult.content as string
        );
      }

      // 5. 准备编辑器内容
      const mockNote = {
        id: "test",
        content: input,
        contentFormat: "markdown" as const,
        x: 0,
        y: 0,
        width: 300,
        height: 200,
        title: "Test Note",
        isEditing: false,
        isTitleEditing: false,
        color: "yellow" as const,
        isNew: false,
        zIndex: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const editorContent = contentAdapter.prepareForEditor(mockNote);

      setConversionResults({
        markdownToJSON: markdownToJSONResult,
        jsonToMarkdown: jsonToMarkdownResult,
        markdownToHTML: markdownToHTMLResult,
        htmlToMarkdown: htmlToMarkdownResult,
        editorContent,
      });

      setTestStatus("completed");
    } catch (error) {
      console.error("Test failed:", error);
      setTestStatus("idle");
    }
  }, [converter, testInput, customMarkdown]);

  // 清空结果
  const clearResults = useCallback(() => {
    setConversionResults({});
    setTestStatus("idle");
  }, []);

  // 处理编辑器内容变化
  const handleEditorChange = useCallback((content: string) => {
    console.log("Editor content changed:", content);
  }, []);

  return (
    <div style={{ padding: "20px", maxWidth: "1200px", margin: "0 auto" }}>
      <Title level={2}>🧪 内容转换器测试</Title>

      <Alert
        message="测试说明"
        description="这个工具用于测试新的 TipTap 原生转换器功能，特别是任务列表、表格等复杂内容的转换。"
        type="info"
        style={{ marginBottom: 24 }}
      />

      <Row gutter={24}>
        {/* 输入区域 */}
        <Col span={12}>
          <Card title="输入测试" style={{ marginBottom: 16 }}>
            <Tabs defaultActiveKey="presets">
              <TabPane tab="预设测试案例" key="presets">
                <Space direction="vertical" style={{ width: "100%" }}>
                  {TEST_CASES.map((testCase, index) => (
                    <Button
                      key={index}
                      type={selectedTest === testCase ? "primary" : "default"}
                      onClick={() => setSelectedTest(testCase)}
                      style={{ width: "100%", textAlign: "left" }}
                    >
                      <div>
                        <div>
                          <strong>{testCase.name}</strong>
                        </div>
                        <div style={{ fontSize: "12px", color: "#666" }}>
                          {testCase.description}
                        </div>
                      </div>
                    </Button>
                  ))}
                </Space>
              </TabPane>

              <TabPane tab="自定义输入" key="custom">
                <TextArea
                  value={customMarkdown}
                  onChange={(e) => setCustomMarkdown(e.target.value)}
                  placeholder="输入自定义的 Markdown 内容..."
                  rows={8}
                />
              </TabPane>
            </Tabs>

            <div style={{ marginTop: 16 }}>
              <Title level={5}>当前测试内容：</Title>
              <TextArea
                value={testInput}
                onChange={(e) => setTestInput(e.target.value)}
                rows={6}
                style={{ fontFamily: "monospace" }}
              />
            </div>

            <Space style={{ marginTop: 16 }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={runConversionTest}
                loading={testStatus === "running"}
              >
                运行测试
              </Button>
              <Button icon={<ClearOutlined />} onClick={clearResults}>
                清空结果
              </Button>
            </Space>
          </Card>

          {/* 期望功能 */}
          {selectedTest && (
            <Card title="期望功能" size="small">
              <Space wrap>
                {selectedTest.expectedFeatures.map((feature, index) => (
                  <Text key={index} code>
                    {feature}
                  </Text>
                ))}
              </Space>
            </Card>
          )}
        </Col>

        {/* 结果区域 */}
        <Col span={12}>
          {testStatus === "completed" && conversionResults.markdownToJSON && (
            <Card title="转换结果" style={{ marginBottom: 16 }}>
              <Tabs>
                <TabPane tab="编辑器预览" key="editor">
                  <div
                    style={{
                      border: "1px solid #d9d9d9",
                      borderRadius: "6px",
                      padding: "12px",
                    }}
                  >
                    <WysiwygEditor
                      content={conversionResults.editorContent || ""}
                      onChange={handleEditorChange}
                      placeholder="编辑器预览..."
                    />
                  </div>
                </TabPane>

                <TabPane tab="JSON 结果" key="json">
                  <div style={{ marginBottom: 12 }}>
                    <Text strong>Markdown → JSON: </Text>
                    {conversionResults.markdownToJSON?.success ? (
                      <Text type="success">
                        <CheckCircleOutlined /> 成功
                      </Text>
                    ) : (
                      <Text type="danger">失败</Text>
                    )}
                  </div>
                  <pre
                    style={{
                      backgroundColor: "#f5f5f5",
                      padding: "12px",
                      borderRadius: "4px",
                      fontSize: "12px",
                      maxHeight: "300px",
                      overflow: "auto",
                    }}
                  >
                    {JSON.stringify(
                      conversionResults.markdownToJSON?.content,
                      null,
                      2
                    )}
                  </pre>
                </TabPane>

                <TabPane tab="往返测试" key="roundtrip">
                  <Space direction="vertical" style={{ width: "100%" }}>
                    <div>
                      <Text strong>JSON → Markdown: </Text>
                      {conversionResults.jsonToMarkdown?.success ? (
                        <Text type="success">
                          <CheckCircleOutlined /> 成功
                        </Text>
                      ) : (
                        <Text type="danger">失败</Text>
                      )}
                    </div>

                    {conversionResults.jsonToMarkdown?.success && (
                      <div>
                        <Paragraph>往返转换结果：</Paragraph>
                        <pre
                          style={{
                            backgroundColor: "#f5f5f5",
                            padding: "12px",
                            borderRadius: "4px",
                            fontSize: "12px",
                            maxHeight: "200px",
                            overflow: "auto",
                          }}
                        >
                          {conversionResults.jsonToMarkdown.content}
                        </pre>
                      </div>
                    )}
                  </Space>
                </TabPane>

                <TabPane tab="HTML 转换" key="html">
                  <Space direction="vertical" style={{ width: "100%" }}>
                    <div>
                      <Text strong>Markdown → HTML: </Text>
                      {conversionResults.markdownToHTML?.success ? (
                        <Text type="success">
                          <CheckCircleOutlined /> 成功
                        </Text>
                      ) : (
                        <Text type="danger">失败</Text>
                      )}
                    </div>

                    {conversionResults.markdownToHTML?.success && (
                      <pre
                        style={{
                          backgroundColor: "#f5f5f5",
                          padding: "12px",
                          borderRadius: "4px",
                          fontSize: "12px",
                          maxHeight: "200px",
                          overflow: "auto",
                        }}
                      >
                        {conversionResults.markdownToHTML.content}
                      </pre>
                    )}
                  </Space>
                </TabPane>
              </Tabs>
            </Card>
          )}

          {testStatus === "idle" && (
            <Card>
              <div style={{ textAlign: "center", padding: "40px" }}>
                <Text type="secondary">点击"运行测试"开始验证转换功能</Text>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default ContentConverterTest;
