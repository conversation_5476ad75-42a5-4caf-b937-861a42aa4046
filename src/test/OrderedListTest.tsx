import React, { useState } from "react";
import { Card, Button, Space, Typography } from "antd";
import StandardEditor from "../components/notes/StandardEditor";

const { Title } = Typography;

/**
 * 有序列表功能测试组件
 */
const OrderedListTest: React.FC = () => {
  const [testContent, setTestContent] = useState("");

  // 测试有序列表基本功能
  const testBasicOrderedList = () => {
    const content = `# 有序列表功能测试

## 1. 基本有序列表
1. 第一项
2. 第二项
3. 第三项

## 2. 嵌套有序列表
1. 主要项目一
   1. 子项目 1.1
   2. 子项目 1.2
      1. 深层子项目 1.2.1
      2. 深层子项目 1.2.2
2. 主要项目二
   1. 子项目 2.1
   2. 子项目 2.2

## 3. 混合列表
1. 有序列表项
   - 无序子项
   - 另一个无序子项
2. 另一个有序列表项
   * 无序子项
   * 带**粗体**的项目
3. 第三个有序列表项

## 4. 包含格式的有序列表
1. **粗体文本**项目
2. *斜体文本*项目
3. \`代码文本\`项目
4. 包含[链接](https://example.com)的项目

## 5. 长内容有序列表
1. 这是一个包含很长内容的列表项，用来测试长文本在有序列表中的显示效果。这个项目包含多个句子，用来验证换行和排版是否正确。
2. 第二个长项目，同样包含大量文本内容，测试编辑器对长内容有序列表的渲染和编辑功能。

## 6. 空行分隔的有序列表
1. 第一组第一项
2. 第一组第二项

1. 第二组第一项（应该重新从1开始编号）
2. 第二组第二项`;

    setTestContent(content);
  };

  // 清空内容
  const clearContent = () => {
    setTestContent("");
  };

  return (
    <div style={{ padding: "20px", maxWidth: "1200px", margin: "0 auto" }}>
      <Title level={2}>有序列表功能测试</Title>

      <Space style={{ marginBottom: "20px" }}>
        <Button type="primary" onClick={testBasicOrderedList}>
          加载测试内容
        </Button>
        <Button onClick={clearContent}>清空内容</Button>
      </Space>

      <div style={{ display: "flex", gap: "20px" }}>
        {/* 左侧：编辑器 */}
        <Card title="编辑器" style={{ flex: 1 }}>
          <StandardEditor
            content={testContent}
            onChange={setTestContent}
            placeholder="在这里测试有序列表功能..."
            autoFocus={false}
          />
        </Card>

        {/* 右侧：原始Markdown */}
        <Card title="Markdown 源码" style={{ flex: 1 }}>
          <pre
            style={{
              background: "#f5f5f5",
              padding: "16px",
              borderRadius: "8px",
              fontSize: "12px",
              maxHeight: "500px",
              overflow: "auto",
              whiteSpace: "pre-wrap",
              wordWrap: "break-word",
            }}
          >
            {testContent}
          </pre>
        </Card>
      </div>

      <Card title="测试要点" style={{ marginTop: "20px" }}>
        <ul>
          <li>✅ 基本有序列表编号是否正确</li>
          <li>✅ 嵌套有序列表编号是否正确</li>
          <li>✅ 工具栏有序列表按钮是否正常工作</li>
          <li>✅ 键盘快捷键（Tab/Shift+Tab）是否正常</li>
          <li>✅ 回车键是否能正确创建新列表项</li>
          <li>✅ Backspace键是否能正确退出列表</li>
          <li>✅ 格式化文本在列表项中是否正常显示</li>
          <li>✅ 空行分隔的列表是否正确重新编号</li>
        </ul>
      </Card>
    </div>
  );
};

export default OrderedListTest;
