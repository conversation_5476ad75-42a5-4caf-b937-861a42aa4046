import React, { useEffect, useState } from "react";
import { useEditor, EditorContent, type Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Image from "@tiptap/extension-image";
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";

import { StandardContentConverter } from "../../utils/standardContentConverter";
import StandardTableToolbar from "./editor/StandardTableToolbar";
import StandardTaskListToolbar from "./editor/StandardTaskListToolbar";
import "./WysiwygEditor.css";

/**
 * 标准化编辑器配置接口 - 符合 Tiptap 官方标准
 */
interface StandardEditorConfig {
  /** 是否启用表格功能 */
  enableTable?: boolean;
  /** 是否启用任务列表功能 */
  enableTaskList?: boolean;
  /** 是否启用图片功能 */
  enableImage?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autofocus?: boolean;
  /** 是否可编辑 */
  editable?: boolean;
}

/**
 * 默认编辑器配置
 */
const DEFAULT_CONFIG: StandardEditorConfig = {
  enableTable: true,
  enableTaskList: true,
  enableImage: true,
  placeholder: "开始输入...",
  autofocus: false,
  editable: true,
};

/**
 * 标准化编辑器组件属性接口
 */
interface StandardEditorProps {
  /** 编辑器内容（Markdown格式） */
  content: string;
  /** 内容变化回调函数 */
  onChange: (content: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 是否禁用编辑器 */
  disabled?: boolean;
  /** 编辑器类名 */
  className?: string;
  /** 编辑器实例回调 */
  onEditorReady?: (editor: Editor) => void;
  /** 点击事件回调 */
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 鼠标按下事件回调 */
  onMouseDown?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 内联样式 */
  style?: React.CSSProperties;
  /** 标题属性 */
  title?: string;
  /** 编辑器配置 */
  config?: StandardEditorConfig;
}

/**
 * 创建标准化内容转换器实例
 */
const contentConverter = new StandardContentConverter();

/**
 * 将 Markdown 转换为 HTML
 */
const markdownToHtml = (markdown: string): string => {
  if (!markdown.trim()) return "<p></p>";

  const result = contentConverter.markdownToHTML(markdown);
  return result.success ? (result.content as string) : "<p></p>";
};

/**
 * 将 HTML 转换为 Markdown
 */
const htmlToMarkdown = (html: string): string => {
  if (!html || html === "<p></p>") return "";

  const result = contentConverter.htmlToMarkdown(html);
  return result.success ? (result.content as string) : "";
};

/**
 * 标准化编辑器组件 - 按照 Tiptap 官方标准实现
 */
const StandardEditor: React.FC<StandardEditorProps> = ({
  content,
  onChange,
  placeholder = "开始输入...",
  autoFocus = false,
  disabled = false,
  className = "",
  onEditorReady,
  onClick,
  onMouseDown,
  style,
  title,
  config = DEFAULT_CONFIG,
}) => {
  // 合并配置
  const editorConfig = { ...DEFAULT_CONFIG, ...config };

  // 工具栏状态管理
  const [showTableToolbar, setShowTableToolbar] = useState(false);
  const [showTaskListToolbar, setShowTaskListToolbar] = useState(false);

  // 创建编辑器实例 - 按照官方标准配置
  const editor = useEditor(
    {
      extensions: [
        // 基础扩展包 - 使用官方推荐的默认配置
        StarterKit.configure({
          heading: {
            levels: [1, 2, 3, 4, 5, 6],
          },
        }),

        // 占位符扩展
        Placeholder.configure({
          placeholder: editorConfig.placeholder || placeholder,
          showOnlyWhenEditable: true,
        }),

        // 图片扩展 - 条件性启用
        ...(editorConfig.enableImage
          ? [
              Image.configure({
                inline: true,
                allowBase64: true,
              }),
            ]
          : []),

        // 表格扩展 - 条件性启用
        ...(editorConfig.enableTable
          ? [
              Table.configure({
                resizable: true,
              }),
              TableRow,
              TableHeader,
              TableCell,
            ]
          : []),

        // 任务列表扩展 - 条件性启用
        ...(editorConfig.enableTaskList
          ? [
              TaskList,
              TaskItem.configure({
                nested: true,
              }),
            ]
          : []),
      ],

      // 初始内容
      content: markdownToHtml(content),

      // 编辑器是否可编辑
      editable: editorConfig.editable !== false && !disabled,

      // 自动聚焦
      autofocus: editorConfig.autofocus || autoFocus,

      // 内容更新回调 - 简化处理
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        const markdown = htmlToMarkdown(html);
        onChange(markdown);
      },

      // 创建完成后回调
      onCreate: ({ editor }) => {
        onEditorReady?.(editor);
      },

      // 事务应用后回调 - 工具栏状态管理
      onTransaction: ({ editor }) => {
        if (editorConfig.enableTable) {
          setShowTableToolbar(editor.isActive("table"));
        }
        if (editorConfig.enableTaskList) {
          setShowTaskListToolbar(
            editor.isActive("taskList") || editor.isActive("taskItem")
          );
        }
      },
    },
    [content, editorConfig, placeholder, autoFocus, disabled, onChange]
  );

  // 当外部内容变化时更新编辑器
  useEffect(() => {
    if (!editor || !content) return;

    const currentMarkdown = htmlToMarkdown(editor.getHTML());
    if (content !== currentMarkdown) {
      const newHtml = markdownToHtml(content);
      editor.commands.setContent(newHtml);
    }
  }, [editor, content]);

  // 当 disabled 状态变化时更新编辑器的可编辑状态
  useEffect(() => {
    if (!editor) return;
    editor.setEditable(!disabled);
  }, [disabled, editor]);

  if (!editor) {
    return <div className="wysiwyg-editor-loading">加载编辑器...</div>;
  }

  return (
    <div
      className={`wysiwyg-editor ${className} ${
        disabled ? "disabled" : "editing"
      }`}
      onClick={onClick}
      onMouseDown={onMouseDown}
      style={style}
      title={title}
    >
      {/* 表格工具栏 - 条件性显示 */}
      {editorConfig.enableTable && !disabled && showTableToolbar && (
        <StandardTableToolbar
          editor={editor}
          visible={true}
          className="editor-table-toolbar"
        />
      )}

      {/* 任务列表工具栏 - 条件性显示 */}
      {editorConfig.enableTaskList && !disabled && showTaskListToolbar && (
        <StandardTaskListToolbar
          editor={editor}
          visible={true}
          className="editor-task-list-toolbar"
        />
      )}

      <EditorContent editor={editor} />
    </div>
  );
};

export default StandardEditor;
export type { StandardEditorProps, StandardEditorConfig };
