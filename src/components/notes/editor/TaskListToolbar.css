/**
 * 任务列表工具栏样式
 */

.task-list-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 8px;
  position: relative;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-list-toolbar.compact {
  padding: 4px 8px;
  gap: 4px;
}

.task-list-toolbar-section {
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-list-toolbar .toolbar-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.task-list-toolbar.compact .toolbar-button {
  padding: 4px 6px;
  font-size: 11px;
}

.task-list-toolbar .toolbar-button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.task-list-toolbar .toolbar-button.active {
  background: var(--accent-color);
  color: white;
}

.task-list-toolbar .toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: transparent;
  color: var(--text-disabled);
}

.task-list-toolbar .toolbar-button:disabled:hover {
  background: transparent;
  color: var(--text-disabled);
}

.task-list-toolbar .button-icon {
  display: inline-block;
  font-size: 14px;
  line-height: 1;
}

.task-list-toolbar.compact .button-icon {
  font-size: 12px;
}

.task-list-toolbar .button-text {
  font-size: 12px;
  font-weight: 500;
}

.task-list-toolbar.compact .button-text {
  display: none;
}

.task-list-toolbar .toolbar-divider {
  width: 1px;
  height: 16px;
  background: var(--border-color);
  margin: 0 4px;
}

.task-list-toolbar.compact .toolbar-divider {
  height: 12px;
  margin: 0 2px;
}

/* 深色主题适配 */
[data-theme="dark"] .task-list-toolbar {
  background: var(--bg-secondary-dark, #2a2a2a);
  border-color: var(--border-color-dark, #404040);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .task-list-toolbar .toolbar-button {
  color: var(--text-secondary-dark, #b0b0b0);
}

[data-theme="dark"] .task-list-toolbar .toolbar-button:hover {
  background: var(--bg-hover-dark, #3a3a3a);
  color: var(--text-primary-dark, #ffffff);
}

[data-theme="dark"] .task-list-toolbar .toolbar-button.active {
  background: var(--accent-color-dark, #4a9eff);
  color: white;
}

[data-theme="dark"] .task-list-toolbar .toolbar-divider {
  background: var(--border-color-dark, #404040);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-list-toolbar {
    padding: 6px 8px;
    gap: 6px;
  }

  .task-list-toolbar .toolbar-button {
    padding: 4px 6px;
    font-size: 11px;
  }

  .task-list-toolbar .button-text {
    display: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .task-list-toolbar {
    border-width: 2px;
  }

  .task-list-toolbar .toolbar-button {
    border: 1px solid transparent;
  }

  .task-list-toolbar .toolbar-button:focus {
    border-color: var(--accent-color);
    outline: 2px solid var(--accent-color);
    outline-offset: 1px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .task-list-toolbar .toolbar-button {
    transition: none;
  }
}
