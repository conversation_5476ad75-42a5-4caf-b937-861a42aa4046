/**
 * 任务列表工具栏组件
 * 为编辑器提供任务列表操作界面
 */

import React, { useMemo } from "react";
import type { Editor } from "@tiptap/react";
import "./TaskListToolbar.css";

/**
 * 工具栏按钮配置接口
 */
interface ToolbarButtonConfig {
  /** 按钮唯一标识 */
  id: string;
  /** 按钮标题/提示文本 */
  title: string;
  /** 按钮图标 */
  icon: string | React.ComponentType;
  /** 按钮分组 */
  group: string;
  /** 按钮位置 */
  position?: number;
  /** 快捷键 */
  shortcut?: string;
  /** 点击事件处理 */
  onClick?: (editor: Editor) => void;
  /** 按钮是否激活 */
  isActive?: (editor: Editor) => boolean;
  /** 按钮是否禁用 */
  isDisabled?: (editor: Editor) => boolean;
}

interface TaskListToolbarProps {
  /** 编辑器实例 */
  editor: Editor | null;
  /** 工具栏按钮配置 */
  buttons?: ToolbarButtonConfig[];
  /** 是否显示工具栏 */
  visible?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 是否紧凑模式 */
  compact?: boolean;
}

/**
 * 默认任务列表工具栏按钮配置
 */
const DEFAULT_TASK_LIST_BUTTONS: ToolbarButtonConfig[] = [
  {
    id: "toggleTaskList",
    title: "任务列表 (Mod+Shift+9)",
    icon: "☐",
    group: "taskList",
    position: 1,
    shortcut: "Mod+Shift+9",
    onClick: (editor: Editor) => {
      editor.chain().focus().toggleList("taskList", "taskItem").run();
    },
    isActive: (editor: Editor) => editor.isActive("taskList"),
    isDisabled: (editor: Editor) =>
      !editor.can().toggleList("taskList", "taskItem"),
  },
  {
    id: "divider1",
    title: "",
    icon: "|",
    group: "taskList",
    position: 2,
    onClick: () => {},
  },
  {
    id: "checkTask",
    title: "切换任务状态",
    icon: "✅",
    group: "taskList",
    position: 3,
    onClick: (editor: Editor) => {
      // 获取当前任务项并切换其状态
      const { state } = editor;
      const { selection } = state;
      const { $from } = selection;

      // 查找当前任务项节点
      let taskItemNode = null;
      let taskItemPos = null;

      for (let i = $from.depth; i >= 0; i--) {
        const node = $from.node(i);
        if (node.type.name === "taskItem") {
          taskItemNode = node;
          taskItemPos = $from.before(i);
          break;
        }
      }

      if (taskItemNode && taskItemPos !== null) {
        const newAttrs = {
          ...taskItemNode.attrs,
          checked: !taskItemNode.attrs.checked,
        };

        editor
          .chain()
          .focus()
          .command(({ tr }) => {
            tr.setNodeMarkup(taskItemPos, undefined, newAttrs);
            return true;
          })
          .run();
      }
    },
    isActive: (editor: Editor) => {
      const { state } = editor;
      const { selection } = state;
      const { $from } = selection;

      for (let i = $from.depth; i >= 0; i--) {
        const node = $from.node(i);
        if (node.type.name === "taskItem") {
          return node.attrs.checked === true;
        }
      }
      return false;
    },
    isDisabled: (editor: Editor) => {
      const { state } = editor;
      const { selection } = state;
      const { $from } = selection;

      // 检查是否在任务项中
      for (let i = $from.depth; i >= 0; i--) {
        const node = $from.node(i);
        if (node.type.name === "taskItem") {
          return false;
        }
      }
      return true;
    },
  },
  {
    id: "addTaskItem",
    title: "添加新任务",
    icon: "➕",
    group: "taskList",
    position: 4,
    onClick: (editor: Editor) => {
      editor.chain().focus().splitListItem("taskItem").run();
    },
    isDisabled: (editor: Editor) => !editor.can().splitListItem("taskItem"),
  },
  {
    id: "divider2",
    title: "",
    icon: "|",
    group: "taskList",
    position: 5,
    onClick: () => {},
  },
  {
    id: "convertToBulletList",
    title: "转换为普通列表",
    icon: "📝",
    group: "taskList",
    position: 6,
    onClick: (editor: Editor) => {
      editor.chain().focus().toggleBulletList().run();
    },
    isDisabled: (editor: Editor) => !editor.can().toggleBulletList(),
  },
];

/**
 * 任务列表工具栏组件
 */
const TaskListToolbar: React.FC<TaskListToolbarProps> = ({
  editor,
  buttons = DEFAULT_TASK_LIST_BUTTONS,
  visible = true,
  className = "",
  compact = false,
}) => {
  // 过滤和排序按钮
  const sortedButtons = useMemo(() => {
    return buttons
      .filter((button) => button.group === "taskList")
      .sort((a, b) => (a.position || 100) - (b.position || 100));
  }, [buttons]);

  // 检查是否在任务列表中
  const isInTaskList = useMemo(() => {
    if (!editor) return false;
    return editor.isActive("taskList") || editor.isActive("taskItem");
  }, [editor]);

  if (!visible || !editor) {
    return null;
  }

  const handleButtonClick = (
    button: ToolbarButtonConfig,
    event: React.MouseEvent
  ) => {
    event.preventDefault();
    event.stopPropagation();

    if (button.onClick && editor) {
      button.onClick(editor);
    }
  };

  const isButtonActive = (button: ToolbarButtonConfig): boolean => {
    if (!button.isActive || !editor) return false;
    return button.isActive(editor);
  };

  const isButtonDisabled = (button: ToolbarButtonConfig): boolean => {
    if (!editor) return true;
    if (button.isDisabled) {
      return button.isDisabled(editor);
    }
    return false;
  };

  return (
    <div
      className={`task-list-toolbar ${compact ? "compact" : ""} ${className}`}
    >
      {/* 任务列表切换按钮始终显示 */}
      <div className="task-list-toolbar-section">
        {sortedButtons
          .filter((button) => button.id === "toggleTaskList")
          .map((button) => (
            <button
              key={button.id}
              className={`toolbar-button ${
                isButtonActive(button) ? "active" : ""
              }`}
              onClick={(e) => handleButtonClick(button, e)}
              disabled={isButtonDisabled(button)}
              title={button.title}
              type="button"
            >
              {typeof button.icon === "string" ? (
                <span className="button-icon">{button.icon}</span>
              ) : React.isValidElement(button.icon) ? (
                button.icon
              ) : (
                <button.icon />
              )}
              {!compact && <span className="button-text">任务列表</span>}
            </button>
          ))}
      </div>

      {/* 任务列表操作按钮 - 只在任务列表中显示 */}
      {isInTaskList && (
        <>
          <div className="toolbar-divider" />
          <div className="task-list-toolbar-section">
            {sortedButtons
              .filter((button) => button.id !== "toggleTaskList")
              .map((button) => {
                // 分割线特殊处理
                if (button.icon === "|") {
                  return <div key={button.id} className="toolbar-divider" />;
                }

                return (
                  <button
                    key={button.id}
                    className={`toolbar-button ${
                      isButtonActive(button) ? "active" : ""
                    }`}
                    onClick={(e) => handleButtonClick(button, e)}
                    disabled={isButtonDisabled(button)}
                    title={button.title}
                    type="button"
                  >
                    {typeof button.icon === "string" ? (
                      <span className="button-icon">{button.icon}</span>
                    ) : React.isValidElement(button.icon) ? (
                      button.icon
                    ) : (
                      <button.icon />
                    )}
                  </button>
                );
              })}
          </div>
        </>
      )}
    </div>
  );
};

export default TaskListToolbar;
