/**
 * 标准化任务列表工具栏组件
 * 按照 Tiptap 官方最佳实践重写
 */

import React from "react";
import type { Editor } from "@tiptap/react";
import "./TaskListToolbar.css";

interface StandardTaskListToolbarProps {
  /** 编辑器实例 */
  editor: Editor | null;
  /** 是否显示工具栏 */
  visible?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 任务列表操作按钮配置
 */
const TASK_LIST_ACTIONS = [
  {
    id: "toggleTaskList",
    title: "切换任务列表",
    icon: "☐",
    action: (editor: Editor) => {
      editor.chain().focus().toggleTaskList().run();
    },
    isActive: (editor: Editor) => editor.isActive("taskList"),
    isDisabled: (editor: Editor) => !editor.can().toggleTaskList(),
  },
  {
    id: "splitTaskItem",
    title: "添加新任务",
    icon: "➕",
    action: (editor: Editor) => {
      editor.chain().focus().splitListItem("taskItem").run();
    },
    isDisabled: (editor: Editor) => !editor.can().splitListItem("taskItem"),
  },
  {
    id: "sinkTaskItem",
    title: "增加缩进",
    icon: "➡️",
    action: (editor: Editor) => {
      editor.chain().focus().sinkListItem("taskItem").run();
    },
    isDisabled: (editor: Editor) => !editor.can().sinkListItem("taskItem"),
  },
  {
    id: "liftTaskItem",
    title: "减少缩进",
    icon: "⬅️",
    action: (editor: Editor) => {
      editor.chain().focus().liftListItem("taskItem").run();
    },
    isDisabled: (editor: Editor) => !editor.can().liftListItem("taskItem"),
  },
  {
    id: "toggleBulletList",
    title: "转换为普通列表",
    icon: "📝",
    action: (editor: Editor) => {
      editor.chain().focus().toggleBulletList().run();
    },
    isDisabled: (editor: Editor) => !editor.can().toggleBulletList(),
  },
];

/**
 * 标准化任务列表工具栏组件
 */
const StandardTaskListToolbar: React.FC<StandardTaskListToolbarProps> = ({
  editor,
  visible = true,
  className = "",
}) => {
  if (!editor || !visible) {
    return null;
  }

  return (
    <div className={`task-list-toolbar ${className}`}>
      <div className="task-list-toolbar-content">
        {TASK_LIST_ACTIONS.map((action) => {
          const isDisabled = action.isDisabled(editor);
          const isActive = action.isActive?.(editor) || false;
          
          return (
            <button
              key={action.id}
              type="button"
              className={`task-list-toolbar-button ${isDisabled ? "disabled" : ""} ${
                isActive ? "active" : ""
              }`}
              title={action.title}
              disabled={isDisabled}
              onClick={() => {
                if (!isDisabled) {
                  action.action(editor);
                }
              }}
            >
              <span className="button-icon">{action.icon}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default StandardTaskListToolbar;
