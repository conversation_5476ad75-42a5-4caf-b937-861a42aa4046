/**
 * 标准化表格工具栏组件
 * 按照 Tiptap 官方最佳实践重写
 */

import React from "react";
import type { Editor } from "@tiptap/react";
import "./TableToolbar.css";

interface StandardTableToolbarProps {
  /** 编辑器实例 */
  editor: Editor | null;
  /** 是否显示工具栏 */
  visible?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 表格操作按钮配置
 */
const TABLE_ACTIONS = [
  {
    id: "insertTable",
    title: "插入表格",
    icon: "📋",
    action: (editor: Editor) => {
      editor.chain().focus().insertTable({ rows: 3, cols: 3 }).run();
    },
    isDisabled: (editor: Editor) => !editor.can().insertTable(),
  },
  {
    id: "deleteTable",
    title: "删除表格",
    icon: "🗑️",
    action: (editor: Editor) => {
      editor.chain().focus().deleteTable().run();
    },
    isDisabled: (editor: Editor) => !editor.can().deleteTable(),
  },
  {
    id: "addColumnBefore",
    title: "在前面插入列",
    icon: "⬅️➕",
    action: (editor: Editor) => {
      editor.chain().focus().addColumnBefore().run();
    },
    isDisabled: (editor: Editor) => !editor.can().addColumnBefore(),
  },
  {
    id: "addColumnAfter",
    title: "在后面插入列",
    icon: "➕➡️",
    action: (editor: Editor) => {
      editor.chain().focus().addColumnAfter().run();
    },
    isDisabled: (editor: Editor) => !editor.can().addColumnAfter(),
  },
  {
    id: "deleteColumn",
    title: "删除列",
    icon: "❌",
    action: (editor: Editor) => {
      editor.chain().focus().deleteColumn().run();
    },
    isDisabled: (editor: Editor) => !editor.can().deleteColumn(),
  },
  {
    id: "addRowBefore",
    title: "在上面插入行",
    icon: "⬆️➕",
    action: (editor: Editor) => {
      editor.chain().focus().addRowBefore().run();
    },
    isDisabled: (editor: Editor) => !editor.can().addRowBefore(),
  },
  {
    id: "addRowAfter",
    title: "在下面插入行",
    icon: "➕⬇️",
    action: (editor: Editor) => {
      editor.chain().focus().addRowAfter().run();
    },
    isDisabled: (editor: Editor) => !editor.can().addRowAfter(),
  },
  {
    id: "deleteRow",
    title: "删除行",
    icon: "🗑️",
    action: (editor: Editor) => {
      editor.chain().focus().deleteRow().run();
    },
    isDisabled: (editor: Editor) => !editor.can().deleteRow(),
  },
];

/**
 * 标准化表格工具栏组件
 */
const StandardTableToolbar: React.FC<StandardTableToolbarProps> = ({
  editor,
  visible = true,
  className = "",
}) => {
  if (!editor || !visible) {
    return null;
  }

  return (
    <div className={`table-toolbar ${className}`}>
      <div className="table-toolbar-content">
        {TABLE_ACTIONS.map((action) => {
          const isDisabled = action.isDisabled(editor);
          
          return (
            <button
              key={action.id}
              type="button"
              className={`table-toolbar-button ${isDisabled ? "disabled" : ""}`}
              title={action.title}
              disabled={isDisabled}
              onClick={() => {
                if (!isDisabled) {
                  action.action(editor);
                }
              }}
            >
              <span className="button-icon">{action.icon}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default StandardTableToolbar;
