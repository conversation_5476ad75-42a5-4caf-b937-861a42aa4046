/**
 * 表格工具栏组件
 * 为编辑器提供表格操作界面
 */

import React, { useMemo } from "react";
import type { Editor } from "@tiptap/react";
import "./TableToolbar.css";

/**
 * 工具栏按钮配置接口
 */
interface ToolbarButtonConfig {
  /** 按钮唯一标识 */
  id: string;
  /** 按钮标题/提示文本 */
  title: string;
  /** 按钮图标 */
  icon: string | React.ComponentType;
  /** 按钮分组 */
  group: string;
  /** 按钮位置 */
  position?: number;
  /** 快捷键 */
  shortcut?: string;
  /** 点击事件处理 */
  onClick?: (editor: Editor) => void;
  /** 按钮是否激活 */
  isActive?: (editor: Editor) => boolean;
  /** 按钮是否禁用 */
  isDisabled?: (editor: Editor) => boolean;
}

interface TableToolbarProps {
  /** 编辑器实例 */
  editor: Editor | null;
  /** 工具栏按钮配置 */
  buttons?: ToolbarButtonConfig[];
  /** 是否显示工具栏 */
  visible?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 是否紧凑模式 */
  compact?: boolean;
}

/**
 * 默认表格工具栏按钮配置
 */
const DEFAULT_TABLE_BUTTONS: ToolbarButtonConfig[] = [
  {
    id: "insertTable",
    title: "插入表格 (Mod+Alt+T)",
    icon: "📋",
    group: "table",
    position: 1,
    shortcut: "Mod+Alt+T",
    onClick: (editor: Editor) => {
      editor.chain().focus().insertTable({ rows: 3, cols: 3 }).run();
    },
    isDisabled: (editor: Editor) => !editor.can().insertTable(),
  },
  {
    id: "deleteTable",
    title: "删除表格",
    icon: "🗑️",
    group: "table",
    position: 2,
    onClick: (editor: Editor) => {
      editor.chain().focus().deleteTable().run();
    },
    isActive: (editor: Editor) => editor.isActive("table"),
    isDisabled: (editor: Editor) => !editor.can().deleteTable(),
  },
  {
    id: "toggleHeaderRow",
    title: "切换标题行",
    icon: "📋",
    group: "table",
    position: 3,
    onClick: (editor: Editor) => {
      editor.chain().focus().toggleHeaderRow().run();
    },
    isActive: (editor: Editor) => editor.isActive("tableHeader"),
  },
];

/**
 * 表格工具栏组件
 */
const TableToolbar: React.FC<TableToolbarProps> = ({
  editor,
  buttons = DEFAULT_TABLE_BUTTONS,
  visible = true,
  className = "",
  compact = false,
}) => {
  // 过滤和排序按钮
  const sortedButtons = useMemo(() => {
    return buttons
      .filter((button) => button.group === "table")
      .sort((a, b) => (a.position || 100) - (b.position || 100));
  }, [buttons]);

  // 检查是否在表格中
  const isInTable = useMemo(() => {
    if (!editor) return false;
    return editor.isActive("table");
  }, [editor]);

  if (!visible || !editor) {
    return null;
  }

  const handleButtonClick = (
    button: ToolbarButtonConfig,
    event: React.MouseEvent
  ) => {
    event.preventDefault();
    event.stopPropagation();

    if (button.onClick && editor) {
      button.onClick(editor);
    }
  };

  const isButtonActive = (button: ToolbarButtonConfig): boolean => {
    if (!button.isActive || !editor) return false;
    return button.isActive(editor);
  };

  const isButtonDisabled = (button: ToolbarButtonConfig): boolean => {
    if (!editor) return true;
    if (button.isDisabled) {
      return button.isDisabled(editor);
    }
    return false;
  };

  return (
    <div className={`table-toolbar ${compact ? "compact" : ""} ${className}`}>
      {/* 插入表格按钮始终显示 */}
      <div className="table-toolbar-section">
        {sortedButtons
          .filter((button) => button.id === "insertTable")
          .map((button) => (
            <button
              key={button.id}
              className={`toolbar-button ${
                isButtonActive(button) ? "active" : ""
              }`}
              onClick={(e) => handleButtonClick(button, e)}
              disabled={isButtonDisabled(button)}
              title={button.title}
              type="button"
            >
              {typeof button.icon === "string" ? (
                <span className="button-icon">{button.icon}</span>
              ) : React.isValidElement(button.icon) ? (
                button.icon
              ) : (
                <button.icon />
              )}
              {!compact && <span className="button-text">插入表格</span>}
            </button>
          ))}
      </div>

      {/* 表格操作按钮 - 只在表格中显示 */}
      {isInTable && (
        <>
          <div className="toolbar-divider" />
          <div className="table-toolbar-section">
            {sortedButtons
              .filter((button) => button.id !== "insertTable")
              .map((button) => {
                // 分割线特殊处理
                if (button.icon === "|") {
                  return <div key={button.id} className="toolbar-divider" />;
                }

                return (
                  <button
                    key={button.id}
                    className={`toolbar-button ${
                      isButtonActive(button) ? "active" : ""
                    }`}
                    onClick={(e) => handleButtonClick(button, e)}
                    disabled={isButtonDisabled(button)}
                    title={button.title}
                    type="button"
                  >
                    {typeof button.icon === "string" ? (
                      <span className="button-icon">{button.icon}</span>
                    ) : React.isValidElement(button.icon) ? (
                      button.icon
                    ) : (
                      <button.icon />
                    )}
                  </button>
                );
              })}
          </div>
        </>
      )}
    </div>
  );
};

export default TableToolbar;
