import React, { useCallback, useEffect, useRef, useState } from "react";
import { useEdit<PERSON>, EditorContent, type Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Image from "@tiptap/extension-image";
// 表格扩展导入
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";
// ListKit 扩展导入
import { ListKit } from "@tiptap/extension-list";

import { useCanvasStore } from "../../stores/canvasStore";
import TableToolbar from "./editor/TableToolbar";
import TaskListToolbar from "./editor/TaskListToolbar";
import "./WysiwygEditor.css";

// 导入原生内容转换器
import { ContentConverter } from "../../utils/contentConverter";

/**
 * 编辑器配置接口
 */
interface EditorConfig {
  /** 是否启用健康检查 */
  healthCheck?: boolean;
  /** 是否启用性能监控 */
  performanceMonitor?: boolean;
  /** 是否启用UX优化 */
  uxOptimizer?: boolean;
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number;
  /** 是否启用智能滚动 */
  smartScroll?: boolean;
  /** 是否启用表格功能 */
  enableTable?: boolean;
  /** 表格工具栏配置 */
  tableToolbar?: {
    enabled?: boolean;
    compact?: boolean;
  };
  /** 是否启用任务列表功能 */
  enableTaskList?: boolean;
  /** 任务列表工具栏配置 */
  taskListToolbar?: {
    enabled?: boolean;
    compact?: boolean;
  };
}

/**
 * 默认编辑器配置
 */
const DEFAULT_EDITOR_CONFIG: EditorConfig = {
  healthCheck: false,
  performanceMonitor: false,
  uxOptimizer: false,
  debounceDelay: 100,
  smartScroll: true,
  enableTable: true,
  tableToolbar: {
    enabled: true,
    compact: false,
  },
  enableTaskList: true,
  taskListToolbar: {
    enabled: true,
    compact: false,
  },
};

/**
 * 编辑器错误处理 Hook
 */
const useEditorErrorHandler = () => {
  const handleError = useCallback((error: Error, context: string) => {
    console.error(`[Editor Error - ${context}]:`, error);
    // 可以在这里添加错误上报逻辑
  }, []);

  return { handleError };
};

/**
 * 安全地执行编辑器命令，避免在编辑器未挂载时出错
 * 优化版本，增加类型安全
 */
const safeEditorCommand = (
  editor: Editor | null,
  command: () => void
): boolean => {
  if (!editor || editor.isDestroyed) {
    return false;
  }

  try {
    command();
    return true;
  } catch (error) {
    console.warn("编辑器命令执行失败:", error);
    return false;
  }
};

/**
 * 所见即所得编辑器组件属性接口
 */
interface WysiwygEditorProps {
  /** 编辑器内容（Markdown格式） */
  content: string;
  /** 内容变化回调函数 */
  onChange: (content: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 编辑器失焦回调 */
  onBlur?: () => void;
  /** 键盘事件回调 */
  onKeyDown?: (event: KeyboardEvent) => boolean;
  /** 是否禁用编辑器 */
  disabled?: boolean;
  /** 编辑器类名 */
  className?: string;
  /** 编辑器实例回调 */
  onEditorReady?: (editor: Editor) => void;
  /** 点击事件回调 */
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 鼠标按下事件回调 */
  onMouseDown?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 内联样式 */
  style?: React.CSSProperties;
  /** 标题属性 */
  title?: string;
  /** 是否正在流式输入（用于智能滚动） */
  isStreaming?: boolean;
  /** 编辑器配置 */
  config?: EditorConfig;
}

/**
 * 创建内容转换器实例
 * 使用与编辑器相同的扩展配置
 */
const createContentConverter = (): ContentConverter => {
  return new ContentConverter();
};

// 创建转换器实例
const contentConverter = createContentConverter();

/**
 * 将 Markdown 转换为 HTML（用于编辑器初始化）
 * @param markdown Markdown 字符串
 * @returns HTML 字符串
 */
const markdownToHtml = (markdown: string): string => {
  if (!markdown.trim()) return "<p></p>";

  const result = contentConverter.markdownToHTML(markdown);
  if (result.success) {
    return result.content as string;
  } else {
    console.warn("Markdown to HTML conversion failed:", result.error);
    return "<p></p>";
  }
};

/**
 * 将 HTML 转换为 Markdown（用于导出）
 * @param html HTML 字符串
 * @returns Markdown 字符串
 */
const htmlToMarkdown = (html: string): string => {
  if (!html || html === "<p></p>") return "";

  const result = contentConverter.htmlToMarkdown(html);
  if (result.success) {
    return result.content as string;
  } else {
    console.warn("HTML to Markdown conversion failed:", result.error);
    return "";
  }
};

/**
 * 所见即所得编辑器组件
 * 基于TipTap实现，支持Markdown语法自动识别和转换
 */
const WysiwygEditor: React.FC<WysiwygEditorProps> = ({
  content,
  onChange,
  placeholder = "开始输入...",
  autoFocus = false,
  disabled = false,
  className = "",
  onEditorReady,
  onClick,
  onMouseDown,
  style,
  title,
  isStreaming = false,
  config = DEFAULT_EDITOR_CONFIG,
}) => {
  const { handleError } = useEditorErrorHandler();
  const editorRef = useRef<HTMLDivElement>(null);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const proseMirrorRef = useRef<HTMLElement | null>(null);
  const viewReadyRef = useRef<boolean>(false); // 标记视图是否已准备好
  const lastContentLengthRef = useRef<number>(0); // 记录上次内容长度，用于检测内容增长
  const scrollbarCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null); // 滚动条检测防抖
  const isComposingRef = useRef<boolean>(false); // IME输入法合成状态
  const [showTableToolbar, setShowTableToolbar] = useState(false);
  const [showTaskListToolbar, setShowTaskListToolbar] = useState(false);

  // 创建编辑器实例
  const editor = useEditor(
    {
      extensions: [
        // 基础扩展包
        StarterKit.configure({
          // 禁用 StarterKit 中的列表扩展，改用 ListKit
          bulletList: false,
          orderedList: false,
          listItem: false,
          listKeymap: false, // 禁用 StarterKit 中的 listKeymap，改用 ListKit 中的
          // 保持其他配置
          italic: {
            HTMLAttributes: {
              class: "italic-text",
            },
          },
          // 禁用标题ID生成
          heading: {
            levels: [1, 2, 3, 4, 5, 6],
          },
          // 可选：重新启用代码块和水平线
          codeBlock: false,
          horizontalRule: false,
        }),

        // ListKit - 包含 BulletList, OrderedList, ListItem, TaskList, TaskItem, ListKeymap
        ListKit.configure({
          // 配置无序列表
          bulletList: {
            keepMarks: true,
            keepAttributes: false,
            HTMLAttributes: {
              class: "bullet-list",
            },
          },
          // 配置有序列表
          orderedList: {
            keepMarks: true,
            keepAttributes: false,
            HTMLAttributes: {
              class: "ordered-list",
            },
          },
          // 配置列表项
          listItem: {
            HTMLAttributes: {
              class: "list-item",
            },
          },
          // 配置任务列表
          taskList: {
            HTMLAttributes: {
              class: "task-list",
            },
          },
          // 配置任务项
          taskItem: {
            nested: true,
            HTMLAttributes: {
              class: "task-item",
            },
          },
        }),

        // 占位符扩展
        Placeholder.configure({
          placeholder,
          emptyEditorClass: "is-editor-empty",
        }),

        // 图片扩展
        Image.configure({
          inline: true,
          allowBase64: true,
          HTMLAttributes: {
            class: "editor-image",
          },
        }),

        // 表格扩展
        Table.configure({
          resizable: true,
          handleWidth: 5,
          cellMinWidth: 25,
          HTMLAttributes: {
            class: "editor-table",
          },
        }),
        TableRow.configure({
          HTMLAttributes: {
            class: "editor-table-row",
          },
        }),
        TableHeader.configure({
          HTMLAttributes: {
            class: "editor-table-header",
          },
        }),
        TableCell.configure({
          HTMLAttributes: {
            class: "editor-table-cell",
          },
        }),
      ],

      // 初始内容
      content: markdownToHtml(content),

      // 编辑器是否可编辑
      editable: !disabled,

      // 自动聚焦
      autofocus: autoFocus ? "end" : false,

      // 内容更新回调
      onUpdate: ({ editor }) => {
        try {
          // 如果正在进行IME输入法合成，延迟处理避免中断输入
          if (isComposingRef.current) {
            return;
          }

          // 转换HTML为Markdown
          const html = editor.getHTML();
          const markdown = htmlToMarkdown(html);

          // 立即调用onChange，避免输入延迟
          // 防抖仅用于优化频繁更新，不阻塞用户输入体验
          if (updateTimeoutRef.current) {
            clearTimeout(updateTimeoutRef.current);
          }

          // 立即更新以保证输入流畅性
          onChange(markdown);

          // 防抖处理额外的优化操作（如滚动条检测等）
          updateTimeoutRef.current = setTimeout(() => {
            // 延迟执行非关键操作
            checkScrollbarState();
          }, 50); // 减少到50ms，只用于优化操作
        } catch (error) {
          handleError(error as Error, "onUpdate");
        }
      },

      // 创建完成后回调
      onCreate: ({ editor }) => {
        try {
          viewReadyRef.current = true;
          onEditorReady?.(editor);
        } catch (error) {
          handleError(error as Error, "onCreate");
        }
      },

      // 事务应用后回调
      onTransaction: ({ editor, transaction }) => {
        try {
          // 检测表格选择变化
          if (transaction.docChanged || transaction.selectionSet) {
            const isTableSelected = editor.isActive("table");
            if (isTableSelected !== showTableToolbar) {
              setShowTableToolbar(isTableSelected);
            }

            // 检测任务列表选择变化
            const isTaskListSelected =
              editor.isActive("taskList") || editor.isActive("taskItem");
            if (isTaskListSelected !== showTaskListToolbar) {
              setShowTaskListToolbar(isTaskListSelected);
            }
          }
        } catch (error) {
          handleError(error as Error, "onTransaction");
        }
      },
    },
    [placeholder, disabled, autoFocus]
  );

  // 监听画布缩放状态
  const canvasScale = useCanvasStore((state) => state.scale);

  // 监听画布缩放状态（保持原有逻辑）

  // 智能滚动到底部的函数
  const scrollToBottom = useCallback((smooth: boolean = true) => {
    if (proseMirrorRef.current) {
      const element = proseMirrorRef.current;

      // 检查是否需要滚动（内容超出可视区域）
      if (element.scrollHeight > element.clientHeight) {
        element.scrollTo({
          top: element.scrollHeight,
          behavior: smooth ? "smooth" : "auto",
        });

        // 自动滚动完成
      }
    }
  }, []);

  // 检测滚动条状态的函数
  const checkScrollbarState = useCallback(() => {
    if (proseMirrorRef.current) {
      const element = proseMirrorRef.current;

      // 确保元素已经完全渲染
      if (element.offsetHeight === 0 || element.offsetWidth === 0) {
        // 如果元素尺寸为0，延迟重试
        setTimeout(() => checkScrollbarState(), 10);
        return;
      }

      // 等待一帧确保所有样式都已应用
      requestAnimationFrame(() => {
        const hasVerticalScrollbar =
          element.scrollHeight > element.clientHeight;

        // 设置data属性用于CSS选择器
        element.setAttribute(
          "data-scrollable",
          hasVerticalScrollbar.toString()
        );

        // 为父容器添加/移除类名（兼容不支持:has()的浏览器）
        const contentContainer = element.closest(".sticky-note-content");
        if (contentContainer) {
          if (hasVerticalScrollbar) {
            contentContainer.classList.add("has-scrollbar");
          } else {
            contentContainer.classList.remove("has-scrollbar");
          }
        }
      });
    }
  }, [canvasScale]);

  // 防抖版滚动条状态检测
  const debouncedCheckScrollbarState = useCallback(() => {
    if (scrollbarCheckTimeoutRef.current) {
      clearTimeout(scrollbarCheckTimeoutRef.current);
    }

    scrollbarCheckTimeoutRef.current = setTimeout(() => {
      checkScrollbarState();
    }, 16); // 约1帧的延迟
  }, [checkScrollbarState]);

  // ...existing code...

  // 🎯 新增: 挂载ProseMirror DOM引用和IME事件监听
  // 这个Effect确保我们能正确获取到由EditorContent渲染出的可滚动DOM元素
  useEffect(() => {
    if (editorRef.current) {
      // ProseMirror是TipTap渲染出的可编辑区域的类名
      proseMirrorRef.current =
        editorRef.current.querySelector<HTMLElement>(".ProseMirror");

      // 添加IME输入法事件监听器，避免输入法合成期间的内容更新干扰
      if (proseMirrorRef.current) {
        const handleCompositionStart = () => {
          isComposingRef.current = true;
        };

        const handleCompositionEnd = () => {
          isComposingRef.current = false;
          // 合成结束后，触发一次内容更新
          if (editor) {
            const html = editor.getHTML();
            const markdown = htmlToMarkdown(html);
            onChange(markdown);
          }
        };

        proseMirrorRef.current.addEventListener(
          "compositionstart",
          handleCompositionStart
        );
        proseMirrorRef.current.addEventListener(
          "compositionend",
          handleCompositionEnd
        );

        // 清理函数
        return () => {
          if (proseMirrorRef.current) {
            proseMirrorRef.current.removeEventListener(
              "compositionstart",
              handleCompositionStart
            );
            proseMirrorRef.current.removeEventListener(
              "compositionend",
              handleCompositionEnd
            );
          }
        };
      }
    }
    // 依赖editor实例，确保在编辑器创建后执行
  }, [editor, onChange]);

  // ...existing code...

  // 当外部内容变化时更新编辑器
  useEffect(() => {
    if (!editor) return;

    const currentMarkdown = htmlToMarkdown(editor.getHTML());
    // 只有当内容真正不同时才更新，避免无限循环
    if (content !== currentMarkdown) {
      const newHtml = markdownToHtml(content);

      // 🎯 检测是否是流式输入：使用传入的isStreaming属性
      const isContentGrowing = content.length > lastContentLengthRef.current;

      // 使用 setContent 而不是 insertContent 来替换全部内容
      editor.commands.setContent(newHtml, { emitUpdate: false }); // 不触发 onUpdate

      // 内容更新后检测滚动条状态
      setTimeout(checkScrollbarState, 50);

      // 🎯 流式输入时自动滚动到底部
      if (isContentGrowing && isStreaming && config.smartScroll) {
        setTimeout(() => {
          scrollToBottom(true); // 使用平滑滚动
        }, 100);
      }

      lastContentLengthRef.current = content.length;
    }
  }, [content, editor, checkScrollbarState, scrollToBottom, isStreaming]);

  // 当 disabled 状态变化时更新编辑器的可编辑状态
  useEffect(() => {
    if (!editor) return;

    editor.setEditable(!disabled);

    // 如果启用编辑且需要自动聚焦
    if (!disabled && autoFocus) {
      setTimeout(() => {
        safeEditorCommand(editor, () => editor.commands.focus());
      }, 200);
    }
  }, [disabled, autoFocus, editor]);

  // 监听编辑器尺寸变化以检测滚动条状态
  useEffect(() => {
    if (!editor || !proseMirrorRef.current) return;

    const element = proseMirrorRef.current;

    // 创建ResizeObserver监听尺寸变化
    const resizeObserver = new ResizeObserver(() => {
      // 使用防抖版本减少频繁检测
      debouncedCheckScrollbarState();
    });

    resizeObserver.observe(element);

    // 也监听内容变化
    const mutationObserver = new MutationObserver(() => {
      // 使用防抖版本减少频繁检测
      debouncedCheckScrollbarState();
    });

    mutationObserver.observe(element, {
      childList: true,
      subtree: true,
      characterData: true,
    });

    // 清理函数
    return () => {
      resizeObserver.disconnect();
      mutationObserver.disconnect();
    };
  }, [editor, debouncedCheckScrollbarState]);

  // 监听画布缩放变化，重新检测滚动条状态
  useEffect(() => {
    // 当画布缩放发生变化时，字体大小会改变，内容高度也会改变
    // 需要重新检测是否需要滚动条
    if (proseMirrorRef.current) {
      // 使用多次检测确保字体大小变化已经完全应用到DOM
      // 第一次检测：立即检测
      checkScrollbarState();

      // 第二次检测：短延迟后检测，确保CSS变量已应用
      setTimeout(() => {
        checkScrollbarState();
      }, 50);

      // 第三次检测：较长延迟后检测，确保所有渲染完成
      setTimeout(() => {
        checkScrollbarState();
      }, 150);
    }
  }, [canvasScale, checkScrollbarState]);

  // 组件卸载时销毁编辑器和清理定时器
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      if (scrollbarCheckTimeoutRef.current) {
        clearTimeout(scrollbarCheckTimeoutRef.current);
      }
      // 重置视图准备状态
      viewReadyRef.current = false;
      proseMirrorRef.current = null;
      editor?.destroy();
    };
  }, [editor]);

  if (!editor) {
    return <div className="wysiwyg-editor-loading">加载编辑器...</div>;
  }

  return (
    <div
      ref={editorRef}
      className={`wysiwyg-editor ${className} ${
        disabled ? "disabled" : "editing"
      }`}
      onClick={onClick}
      onMouseDown={onMouseDown}
      style={style}
      title={title}
    >
      {/* 表格工具栏 - 仅在启用表格功能且处于编辑状态时显示 */}
      {config.enableTable &&
        config.tableToolbar?.enabled &&
        !disabled &&
        showTableToolbar && (
          <TableToolbar
            editor={editor}
            visible={true}
            compact={config.tableToolbar.compact}
            className="editor-table-toolbar"
          />
        )}

      {/* 任务列表工具栏 - 仅在启用任务列表功能且处于编辑状态时显示 */}
      {config.enableTaskList &&
        config.taskListToolbar?.enabled &&
        !disabled &&
        showTaskListToolbar && (
          <TaskListToolbar
            editor={editor}
            visible={true}
            compact={config.taskListToolbar.compact}
            className="editor-task-list-toolbar"
          />
        )}

      <EditorContent editor={editor} />
    </div>
  );
};

export default WysiwygEditor;
export { safeEditorCommand };
