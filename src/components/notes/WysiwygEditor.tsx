import React, { useEffect, useState } from "react";
import { useEditor, EditorContent, type Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Image from "@tiptap/extension-image";
// 表格扩展导入 - 按照官方文档标准导入
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";
// 任务列表扩展导入 - 使用官方推荐的方式
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";

import TableToolbar from "./editor/TableToolbar";
import TaskListToolbar from "./editor/TaskListToolbar";
import "./WysiwygEditor.css";

// 导入标准化内容转换器
import { ContentConverter } from "../../utils/contentConverter";

/**
 * 编辑器配置接口 - 按照 Tiptap 官方标准简化
 */
interface EditorConfig {
  /** 是否启用表格功能 */
  enableTable?: boolean;
  /** 是否启用任务列表功能 */
  enableTaskList?: boolean;
  /** 是否启用图片功能 */
  enableImage?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autofocus?: boolean;
  /** 是否可编辑 */
  editable?: boolean;
}

/**
 * 默认编辑器配置 - 符合官方推荐
 */
const DEFAULT_EDITOR_CONFIG: EditorConfig = {
  enableTable: true,
  enableTaskList: true,
  enableImage: true,
  placeholder: "开始输入...",
  autofocus: false,
  editable: true,
};

/**
 * 所见即所得编辑器组件属性接口
 */
interface WysiwygEditorProps {
  /** 编辑器内容（Markdown格式） */
  content: string;
  /** 内容变化回调函数 */
  onChange: (content: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 编辑器失焦回调 */
  onBlur?: () => void;
  /** 键盘事件回调 */
  onKeyDown?: (event: KeyboardEvent) => boolean;
  /** 是否禁用编辑器 */
  disabled?: boolean;
  /** 编辑器类名 */
  className?: string;
  /** 编辑器实例回调 */
  onEditorReady?: (editor: Editor) => void;
  /** 点击事件回调 */
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 鼠标按下事件回调 */
  onMouseDown?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 内联样式 */
  style?: React.CSSProperties;
  /** 标题属性 */
  title?: string;
  /** 是否正在流式输入（用于智能滚动） */
  isStreaming?: boolean;
  /** 编辑器配置 */
  config?: EditorConfig;
}

/**
 * 创建内容转换器实例
 * 使用与编辑器相同的扩展配置
 */
const createContentConverter = (): ContentConverter => {
  return new ContentConverter();
};

// 创建转换器实例
const contentConverter = createContentConverter();

/**
 * 将 Markdown 转换为 HTML（用于编辑器初始化）
 * @param markdown Markdown 字符串
 * @returns HTML 字符串
 */
const markdownToHtml = (markdown: string): string => {
  if (!markdown.trim()) return "<p></p>";

  const result = contentConverter.markdownToHTML(markdown);
  if (result.success) {
    return result.content as string;
  } else {
    console.warn("Markdown to HTML conversion failed:", result.error);
    return "<p></p>";
  }
};

/**
 * 将 HTML 转换为 Markdown（用于导出）
 * @param html HTML 字符串
 * @returns Markdown 字符串
 */
const htmlToMarkdown = (html: string): string => {
  if (!html || html === "<p></p>") return "";

  const result = contentConverter.htmlToMarkdown(html);
  if (result.success) {
    return result.content as string;
  } else {
    console.warn("HTML to Markdown conversion failed:", result.error);
    return "";
  }
};

/**
 * 所见即所得编辑器组件
 * 基于TipTap实现，支持Markdown语法自动识别和转换
 */
const WysiwygEditor: React.FC<WysiwygEditorProps> = ({
  content,
  onChange,
  placeholder = "开始输入...",
  autoFocus = false,
  disabled = false,
  className = "",
  onEditorReady,
  onClick,
  onMouseDown,
  style,
  title,

  config = DEFAULT_EDITOR_CONFIG,
}) => {
  // 简化状态管理 - 只保留必要的状态
  const [showTableToolbar, setShowTableToolbar] = useState(false);
  const [showTaskListToolbar, setShowTaskListToolbar] = useState(false);

  // 创建编辑器实例 - 按照官方标准配置
  const editor = useEditor(
    {
      extensions: [
        // 基础扩展包 - 使用官方推荐的默认配置
        StarterKit.configure({
          // 保持简洁的配置，只配置必要的选项
          heading: {
            levels: [1, 2, 3, 4, 5, 6],
          },
        }),

        // 任务列表扩展 - 条件性启用，使用官方推荐方式
        ...(config.enableTaskList
          ? [
              TaskList,
              TaskItem.configure({
                nested: true,
              }),
            ]
          : []),

        // 占位符扩展 - 使用官方推荐配置
        Placeholder.configure({
          placeholder: config.placeholder || placeholder,
          showOnlyWhenEditable: true,
        }),

        // 图片扩展 - 条件性启用
        ...(config.enableImage
          ? [
              Image.configure({
                inline: true,
                allowBase64: true,
              }),
            ]
          : []),

        // 表格扩展 - 条件性启用，使用官方推荐配置
        ...(config.enableTable
          ? [
              Table.configure({
                resizable: true,
              }),
              TableRow,
              TableHeader,
              TableCell,
            ]
          : []),
      ],

      // 初始内容
      content: markdownToHtml(content),

      // 编辑器是否可编辑
      editable: config.editable !== false && !disabled,

      // 自动聚焦 - 使用官方推荐配置
      autofocus: config.autofocus || autoFocus,

      // 内容更新回调 - 简化处理，符合官方标准
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        const markdown = htmlToMarkdown(html);
        onChange(markdown);
      },

      // 创建完成后回调 - 简化处理
      onCreate: ({ editor }) => {
        onEditorReady?.(editor);
      },

      // 事务应用后回调 - 简化工具栏状态管理
      onTransaction: ({ editor }) => {
        if (config.enableTable) {
          setShowTableToolbar(editor.isActive("table"));
        }
        if (config.enableTaskList) {
          setShowTaskListToolbar(
            editor.isActive("taskList") || editor.isActive("taskItem")
          );
        }
      },
    },
    [content, config, placeholder, autoFocus, disabled, onChange]
  );

  // 当外部内容变化时更新编辑器 - 简化处理
  useEffect(() => {
    if (!editor || !content) return;

    const currentMarkdown = htmlToMarkdown(editor.getHTML());
    if (content !== currentMarkdown) {
      const newHtml = markdownToHtml(content);
      editor.commands.setContent(newHtml);
    }
  }, [editor, content]);

  // 当 disabled 状态变化时更新编辑器的可编辑状态
  useEffect(() => {
    if (!editor) return;
    editor.setEditable(!disabled);
  }, [disabled, editor]);

  if (!editor) {
    return <div className="wysiwyg-editor-loading">加载编辑器...</div>;
  }

  return (
    <div
      className={`wysiwyg-editor ${className} ${
        disabled ? "disabled" : "editing"
      }`}
      onClick={onClick}
      onMouseDown={onMouseDown}
      style={style}
      title={title}
    >
      {/* 表格工具栏 - 条件性显示 */}
      {config.enableTable && !disabled && showTableToolbar && (
        <TableToolbar
          editor={editor}
          visible={true}
          className="editor-table-toolbar"
        />
      )}

      {/* 任务列表工具栏 - 条件性显示 */}
      {config.enableTaskList && !disabled && showTaskListToolbar && (
        <TaskListToolbar
          editor={editor}
          visible={true}
          className="editor-task-list-toolbar"
        />
      )}

      <EditorContent editor={editor} />
    </div>
  );
};

export default WysiwygEditor;
