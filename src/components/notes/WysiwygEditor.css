/* 所见即所得编辑器样式 */
.wysiwyg-editor {
  flex: 1; /* 关键：使用flex占满可用空间 */
  width: 100%;
  height: 100%; /* 确保占满父容器高度 */
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.2s ease;
  min-height: 0; /* 关键：允许flex子元素收缩 */
  overflow: hidden; /* 🎯 裁剪溢出内容，确保布局整洁 */
  padding: 0; /* 移除padding，让内部元素完全控制布局 */
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
    "SF Pro Text", "Segoe UI", "Helvetica Neue", "Roboto", "Inter",
    "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "SimSun", "Arial",
    sans-serif;
  font-size: var(--note-content-font-size, 14px);
  line-height: 1.5;
  color: #374151;
}

/* 修复EditorContent生成的包装div */
.wysiwyg-editor > div {
  flex: 1; /* 让EditorContent的包装div也使用flex */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许收缩 */
  overflow: hidden; /* 确保不溢出 */
}

/* 在便签中的禁用状态不应该有透明度 */
.wysiwyg-editor.viewing.disabled {
  opacity: 1 !important;
}

/* 查看状态下的特殊样式 */
.wysiwyg-editor.viewing .ProseMirror {
  min-height: 50px; /* 确保有最小高度 */
  user-select: text; /* 允许选择文本但不能编辑 */
  cursor: default; /* 显示默认光标而不是文本光标 */
}

/* 编辑状态下的样式 */
.wysiwyg-editor.editing .ProseMirror {
  pointer-events: auto;
  user-select: auto;
}

/* 编辑状态下的自定义光标样式 - 与AI生成时保持一致 */
.wysiwyg-editor.editing .ProseMirror {
  caret-color: #1890ff; /* 设置光标颜色为蓝色 */
}

/* 流式状态下的编辑器样式 */
.wysiwyg-editor.streaming .ProseMirror::after {
  content: "▋";
  color: #1890ff;
  font-weight: bold;
  animation: cursorBlink 1s infinite;
  margin-left: 2px;
}

.wysiwyg-editor-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #6b7280;
  font-size: 14px;
}

/* ProseMirror 编辑器核心样式 - 支持通用类名和 tiptap 特定类名 */
.wysiwyg-editor .ProseMirror,
.wysiwyg-editor .tiptap.ProseMirror {
  flex: 1; /* 关键：使用flex占满可用空间 */
  height: 0; /* 关键：让flex完全控制高度 */
  max-height: 100%; /* 确保不超过容器高度 */
  margin: 0;
  outline: none;
  border: none;
  background: transparent;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  transition: all 0.2s ease;
  overflow-y: auto; /* 显示垂直滚动条 */
  overflow-x: hidden; /* 隐藏水平滚动条 */
  word-wrap: break-word;
  white-space: pre-wrap;
  box-sizing: border-box; /* 🎯 关键：padding计入总宽度 */
  resize: none;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;

  /* 🎯 原生对称滚动条布局：由父容器提供内边距 */
  width: 100%; /* 占满容器宽度 */
  padding-left: 0; /* 移除左侧内边距，由父容器提供 */
  padding-right: 0; /* 移除右侧内边距，由父容器提供 */
}

/* 空编辑器占位符样式 */
.wysiwyg-editor .ProseMirror.is-editor-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* 标题样式 */
.wysiwyg-editor .ProseMirror h1 {
  font-size: var(--note-h1-font-size, 18px);
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.wysiwyg-editor .ProseMirror h2 {
  font-size: var(--note-h2-font-size, 16px);
  font-weight: 600;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.wysiwyg-editor .ProseMirror h3 {
  font-size: var(--note-h3-font-size, 15px);
  font-weight: 600;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.wysiwyg-editor .ProseMirror h4,
.wysiwyg-editor .ProseMirror h5,
.wysiwyg-editor .ProseMirror h6 {
  font-size: var(--note-content-font-size, 14px);
  font-weight: 600;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

/* 段落样式 */
.wysiwyg-editor .ProseMirror p {
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.wysiwyg-editor .ProseMirror p:last-child {
  margin-bottom: 0;
}

/* 文本格式样式 */
.wysiwyg-editor .ProseMirror strong {
  font-weight: 600;
}

/* 斜体样式 - 统一支持中英文 */
.wysiwyg-editor .ProseMirror em,
.wysiwyg-editor .ProseMirror .italic-text {
  font-style: italic;
  /* 关键：启用字体合成，让浏览器为不支持斜体的字体自动生成倾斜效果 */
  font-synthesis: style;
  -webkit-font-synthesis: style;
  -moz-font-synthesis: style;
}

/* 对于不支持 font-synthesis 的老版本浏览器，提供CSS变换后备方案 */
@supports not (font-synthesis: style) {
  .wysiwyg-editor .ProseMirror em,
  .wysiwyg-editor .ProseMirror .italic-text {
    display: inline-block;
    transform: skewX(-8deg);
    vertical-align: baseline;
  }
}

.wysiwyg-editor .ProseMirror code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "SF Mono", "Monaco", "Menlo", "Roboto Mono", "Consolas",
    "Liberation Mono", "Courier New", monospace;
  font-size: var(--note-code-font-size, 12px);
}

/* 代码块样式 */
.wysiwyg-editor .ProseMirror pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 8px 0;
  font-family: "SF Mono", "Monaco", "Menlo", "Roboto Mono", "Consolas",
    "Liberation Mono", "Courier New", monospace;
  font-size: var(--note-code-font-size, 12px);
}

.wysiwyg-editor .ProseMirror pre code {
  background: none;
  padding: 0;
  font-size: inherit;
}

/* 列表样式 */
.wysiwyg-editor .ProseMirror ul,
.wysiwyg-editor .ProseMirror ol {
  margin: 8px 0;
  padding-left: 20px;
}

.wysiwyg-editor .ProseMirror ul ul,
.wysiwyg-editor .ProseMirror ol ol,
.wysiwyg-editor .ProseMirror ul ol,
.wysiwyg-editor .ProseMirror ol ul {
  margin: 0;
}

.wysiwyg-editor .ProseMirror li {
  margin: 2px 0;
  line-height: 1.5;
}

.wysiwyg-editor .ProseMirror li p {
  margin: 0;
}

/* 修复：空的有序/无序列表项中光标在数字或圆点后不可见的问题
   说明：部分浏览器在 contenteditable 的列表项(li)中，若内部段落(p)为空，
   会导致无法正确渲染插入符号(caret)。通过为空段落添加一个零宽字符的伪元素，
   为光标提供一个可定位的渲染点，从而让光标出现在编号或圆点的右侧。
   注意：伪元素仅用于渲染，不会进入实际文档内容。 */
.wysiwyg-editor .ProseMirror li > p:empty::before {
  content: "\200b"; /* 零宽空格，确保空段落中也能显示光标 */
  display: inline-block; /* 作为内联块占位，避免高度坍塌 */
}

/* 明确设置列表编号/圆点位置，避免与光标绘制区域重叠 */
.wysiwyg-editor .ProseMirror ol,
.wysiwyg-editor .ProseMirror ul {
  list-style-position: outside; /* 保持编号在内容区域外侧 */
}

/* 任务列表样式 */
.wysiwyg-editor .ProseMirror .task-list {
  list-style: none;
  padding-left: 0;
}

.wysiwyg-editor .ProseMirror .task-item {
  display: flex;
  align-items: flex-start;
  margin: 4px 0;
}

.wysiwyg-editor .ProseMirror .task-item input[type="checkbox"] {
  margin-right: 8px;
  margin-top: 2px;
  cursor: pointer;
}

.wysiwyg-editor .ProseMirror .task-item[data-checked="true"] {
  text-decoration: line-through;
  opacity: 0.7;
}

/* 引用样式 */
.wysiwyg-editor .ProseMirror blockquote {
  border-left: 3px solid currentColor;
  margin: 8px 0;
  padding-left: 12px;
  color: #6b7280;
  opacity: 0.8;
  font-style: italic;
}

/* 链接样式 */
.wysiwyg-editor .ProseMirror .editor-link {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.wysiwyg-editor .ProseMirror .editor-link:hover {
  color: #1d4ed8;
}

/* 图片样式 */
.wysiwyg-editor .ProseMirror .editor-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

/* 分割线样式 */
.wysiwyg-editor .ProseMirror hr {
  border: none;
  height: 1px;
  background: rgba(0, 0, 0, 0.15);
  margin: 16px 0;
}

/* 表格样式 */
.wysiwyg-editor .ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  font-size: var(--note-table-font-size, 12px);
  margin: 8px 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.wysiwyg-editor .ProseMirror table.editor-table {
  position: relative;
}

.wysiwyg-editor .ProseMirror table th,
.wysiwyg-editor .ProseMirror table td {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 6px 8px;
  text-align: left;
  vertical-align: top;
  min-width: 100px;
  position: relative;
}

.wysiwyg-editor .ProseMirror table th {
  background: rgba(0, 0, 0, 0.05);
  font-weight: 600;
  color: #374151;
}

.wysiwyg-editor .ProseMirror table th.editor-table-header {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
}

.wysiwyg-editor .ProseMirror table td.editor-table-cell {
  background: transparent;
}

.wysiwyg-editor .ProseMirror table tr.editor-table-row:nth-child(even) td {
  background: rgba(0, 0, 0, 0.02);
}

.wysiwyg-editor .ProseMirror table tr.editor-table-row:hover td {
  background: rgba(59, 130, 246, 0.05);
}

/* 表格选中状态 */
.wysiwyg-editor .ProseMirror table .selectedCell {
  background: rgba(59, 130, 246, 0.2) !important;
  border-color: #3b82f6 !important;
}

/* 表格调整大小控制 */
.wysiwyg-editor .ProseMirror table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  background: transparent;
  cursor: col-resize;
  z-index: 10;
}

.wysiwyg-editor .ProseMirror table .column-resize-handle:hover {
  background: #3b82f6;
}

/* 表格在移动设备上的响应式设计 */
@media (max-width: 768px) {
  .wysiwyg-editor .ProseMirror table {
    font-size: 11px;
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .wysiwyg-editor .ProseMirror table th,
  .wysiwyg-editor .ProseMirror table td {
    min-width: 20px;
    padding: 4px 6px;
  }
}

/* 表格编辑工具栏样式集成 */
.wysiwyg-editor .editor-table-toolbar {
  margin-bottom: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid #e1e5e9;
}

/* 深色模式下的表格样式 */
@media (prefers-color-scheme: dark) {
  .wysiwyg-editor .ProseMirror table {
    border-color: #4b5563;
  }

  .wysiwyg-editor .ProseMirror table th,
  .wysiwyg-editor .ProseMirror table td {
    border-color: #4b5563;
  }

  .wysiwyg-editor .ProseMirror table th {
    background: rgba(255, 255, 255, 0.1);
    color: #f9fafb;
  }

  .wysiwyg-editor .ProseMirror table th.editor-table-header {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
  }

  .wysiwyg-editor .ProseMirror table tr.editor-table-row:nth-child(even) td {
    background: rgba(255, 255, 255, 0.05);
  }

  .wysiwyg-editor .ProseMirror table tr.editor-table-row:hover td {
    background: rgba(59, 130, 246, 0.1);
  }
}

/* 表格样式 */
.wysiwyg-editor .ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  font-size: var(--note-table-font-size, 12px);
  margin: 8px 0;
}

.wysiwyg-editor .ProseMirror table th,
.wysiwyg-editor .ProseMirror table td {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 6px 8px;
  text-align: left;
  vertical-align: top;
}

.wysiwyg-editor .ProseMirror table th {
  background: rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

/* 选中文本样式 */
.wysiwyg-editor .ProseMirror ::selection {
  background: rgba(59, 130, 246, 0.2);
}

/* 焦点样式 */
.wysiwyg-editor .ProseMirror:focus {
  outline: none;
}

/* 拖拽样式 */
.wysiwyg-editor .ProseMirror .ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wysiwyg-editor {
    font-size: 12px;
  }

  .wysiwyg-editor .ProseMirror h1 {
    font-size: 16px;
  }

  .wysiwyg-editor .ProseMirror h2 {
    font-size: 14px;
  }

  .wysiwyg-editor .ProseMirror h3 {
    font-size: 13px;
  }
}

/* 流式光标动画 */
@keyframes cursorBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .wysiwyg-editor {
    color: #e5e7eb;
  }

  .wysiwyg-editor .ProseMirror.is-editor-empty::before {
    color: #6b7280;
  }

  .wysiwyg-editor .ProseMirror code {
    background: rgba(255, 255, 255, 0.1);
  }

  .wysiwyg-editor .ProseMirror pre {
    background: rgba(255, 255, 255, 0.05);
  }

  .wysiwyg-editor .ProseMirror blockquote {
    color: #9ca3af;
  }

  .wysiwyg-editor .ProseMirror .editor-link {
    color: #60a5fa;
  }

  .wysiwyg-editor .ProseMirror .editor-link:hover {
    color: #93c5fd;
  }

  .wysiwyg-editor .ProseMirror hr {
    background: rgba(255, 255, 255, 0.15);
  }

  .wysiwyg-editor .ProseMirror table th,
  .wysiwyg-editor .ProseMirror table td {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .wysiwyg-editor .ProseMirror table th {
    background: rgba(255, 255, 255, 0.05);
  }

  /* 深色模式下的滚动条样式 - 悬浮显示 */
  .wysiwyg-editor .ProseMirror::-webkit-scrollbar-thumb,
  .wysiwyg-editor .tiptap.ProseMirror::-webkit-scrollbar-thumb {
    background: transparent !important; /* 默认隐藏，覆盖浅色模式样式 */
    transition: background 0.2s ease; /* 添加过渡动画 */
  }

  .wysiwyg-editor .ProseMirror:hover::-webkit-scrollbar-thumb,
  .wysiwyg-editor .tiptap.ProseMirror:hover::-webkit-scrollbar-thumb {
    background: rgba(
      255,
      255,
      255,
      0.3
    ) !important; /* 悬浮时显示，覆盖浅色模式样式 */
  }

  .wysiwyg-editor .ProseMirror::-webkit-scrollbar-thumb:hover,
  .wysiwyg-editor .tiptap.ProseMirror::-webkit-scrollbar-thumb:hover {
    background: rgba(
      255,
      255,
      255,
      0.5
    ) !important; /* 滚动条本身悬浮时加深，覆盖浅色模式样式 */
  }

  /* 深色模式下的 Firefox 滚动条样式 */
  .wysiwyg-editor .ProseMirror,
  .wysiwyg-editor .tiptap.ProseMirror {
    scrollbar-color: transparent transparent !important; /* 默认隐藏，覆盖浅色模式样式 */
  }

  .wysiwyg-editor .ProseMirror:hover,
  .wysiwyg-editor .tiptap.ProseMirror:hover {
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent !important; /* 悬浮时显示，覆盖浅色模式样式 */
  }
}

/* 浅色模式下的便签编辑器滚动条样式 - 悬浮显示 */
.wysiwyg-editor .ProseMirror::-webkit-scrollbar,
.wysiwyg-editor .tiptap.ProseMirror::-webkit-scrollbar {
  width: 17px; /* 与全局滚动条宽度保持一致 */
}

.wysiwyg-editor .ProseMirror::-webkit-scrollbar-track,
.wysiwyg-editor .tiptap.ProseMirror::-webkit-scrollbar-track {
  background: transparent;
}

/* 默认状态：滚动条透明（隐藏） */
.wysiwyg-editor .ProseMirror::-webkit-scrollbar-thumb,
.wysiwyg-editor .tiptap.ProseMirror::-webkit-scrollbar-thumb {
  background-color: transparent; /* 默认隐藏滚动条 */
  border-radius: 4px;
  border: 4px solid transparent;
  background-clip: content-box;
  transition: background-color 0.2s ease; /* 添加过渡动画 */
}

/* 鼠标悬浮在编辑器上时显示滚动条 */
.wysiwyg-editor .ProseMirror:hover::-webkit-scrollbar-thumb,
.wysiwyg-editor .tiptap.ProseMirror:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15); /* 悬浮时显示滚动条 */
}

/* 鼠标悬浮在滚动条本身时加深颜色 */
.wysiwyg-editor .ProseMirror::-webkit-scrollbar-thumb:hover,
.wysiwyg-editor .tiptap.ProseMirror::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.25) !important; /* 滚动条本身悬浮时加深 */
}

/* Firefox 浏览器的便签编辑器滚动条样式 */
.wysiwyg-editor .ProseMirror,
.wysiwyg-editor .tiptap.ProseMirror {
  scrollbar-width: thin; /* 使用细滚动条 */
  scrollbar-color: transparent transparent; /* 默认隐藏滚动条 */
  transition: scrollbar-color 0.2s ease; /* 添加过渡动画 */
}

.wysiwyg-editor .ProseMirror:hover,
.wysiwyg-editor .tiptap.ProseMirror:hover {
  scrollbar-color: rgba(0, 0, 0, 0.15) transparent; /* 悬浮时显示滚动条 */
}

/* 编辑器健康状态样式 */
.wysiwyg-editor.editor-warning {
  border: 2px solid #f59e0b;
  background-color: #fffbeb;
}

.wysiwyg-editor.editor-error {
  border: 2px solid #ef4444;
  background-color: #fef2f2;
}

/* 健康状态指示器 */
.editor-health-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
}

.health-status {
  font-size: 14px;
}

.health-message {
  color: #6b7280;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 任务列表样式 */
.wysiwyg-editor .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0;
}

/* 兼容两种可能的任务项结构：data-type="taskItem" 或仅 data-checked */
.wysiwyg-editor .ProseMirror ul[data-type="taskList"] li[data-type="taskItem"],
.wysiwyg-editor .ProseMirror ul[data-type="taskList"] li[data-checked] {
  display: flex;
  align-items: flex-start;
  margin: 0.125rem 0;
  padding: 0.25rem 0;
  gap: 0.75rem;
  line-height: 1.5;
}

.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-type="taskItem"]
  > label,
.wysiwyg-editor .ProseMirror ul[data-type="taskList"] li[data-checked] > label {
  position: relative;
  display: block;
  width: 14px; /* 勾选框的默认尺寸*/
  height: 14px; /* 勾选框的默认尺寸 */

  /* margin-top: 2px; */
  flex-shrink: 0;
  cursor: pointer;
}

.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-type="taskItem"]
  > label
  input[type="checkbox"],
.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-checked]
  > label
  input[type="checkbox"] {
  appearance: none;
  width: 14px;
  height: 14px;
  min-width: 14px;
  min-height: 14px;
  max-width: 14px;
  max-height: 14px;
  border: 1.5px solid #d1d5db;
  border-radius: 2px;
  background-color: white;
  cursor: pointer;
  position: absolute;
  top: 1px;
  left: 1px;
  transition: all 0.2s ease;
  box-sizing: border-box;
  flex-shrink: 0;
}

.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-type="taskItem"]
  > label
  input[type="checkbox"]:checked,
.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-checked]
  > label
  input[type="checkbox"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-type="taskItem"]
  > label
  input[type="checkbox"]:checked::after,
.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-checked]
  > label
  input[type="checkbox"]:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 9px;
  font-weight: bold;
  line-height: 1;
}

.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-type="taskItem"]
  > div,
.wysiwyg-editor .ProseMirror ul[data-type="taskList"] li[data-checked] > div {
  flex: 1;
  min-width: 0;
  padding-top: 0;
  line-height: 1.4;
}

.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-type="taskItem"][data-checked="true"]
  > div,
.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-checked="true"]
  > div {
  text-decoration: line-through;
  opacity: 0.6;
}

.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-type="taskItem"]:hover
  > label
  input[type="checkbox"],
.wysiwyg-editor
  .ProseMirror
  ul[data-type="taskList"]
  li[data-checked]:hover
  > label
  input[type="checkbox"] {
  border-color: #9ca3af;
}

/* 嵌套任务列表样式 */
.wysiwyg-editor .ProseMirror ul[data-type="taskList"] ul[data-type="taskList"] {
  margin-left: 1.5rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
