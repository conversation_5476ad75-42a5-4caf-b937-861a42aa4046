/**
 * 标准化内容转换工具 - 基于 Tiptap 官方静态渲染器
 * 按照官方标准重写，确保与 StandardEditor 配置一致
 */

import { generateJSON } from "@tiptap/core";
import type { JSONContent } from "@tiptap/react";
import { renderToMarkdown, renderToHTMLString } from "@tiptap/static-renderer";

// 导入标准扩展
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Image from "@tiptap/extension-image";
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";

/**
 * 内容格式常量
 */
export const ContentFormat = {
  MARKDOWN: "markdown",
  HTML: "html",
  JSON: "json",
} as const;

export type ContentFormat = (typeof ContentFormat)[keyof typeof ContentFormat];

/**
 * 转换结果接口
 */
export interface ConvertResult {
  success: boolean;
  content: string | JSONContent;
  originalFormat: ContentFormat;
  targetFormat: ContentFormat;
  error?: string;
}

/**
 * 获取标准扩展配置 - 与 StandardEditor 保持一致
 */
const getStandardExtensions = () => [
  // 基础扩展包 - 使用官方推荐的默认配置
  StarterKit.configure({
    heading: {
      levels: [1, 2, 3, 4, 5, 6],
    },
  }),

  // 占位符扩展
  Placeholder.configure({
    placeholder: "开始输入...",
    showOnlyWhenEditable: true,
  }),

  // 图片扩展
  Image.configure({
    inline: true,
    allowBase64: true,
  }),

  // 表格扩展
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,

  // 任务列表扩展
  TaskList,
  TaskItem.configure({
    nested: true,
  }),
];

/**
 * 标准化内容转换器类
 */
export class StandardContentConverter {
  private extensions: any[];

  constructor() {
    this.extensions = getStandardExtensions();
  }

  /**
   * 获取空文档
   */
  private getEmptyDocument(): JSONContent {
    return {
      type: "doc",
      content: [
        {
          type: "paragraph",
        },
      ],
    };
  }

  /**
   * Markdown 转换为 HTML
   */
  markdownToHTML(markdown: string): ConvertResult {
    try {
      if (!markdown.trim()) {
        return {
          success: true,
          content: "<p></p>",
          originalFormat: ContentFormat.MARKDOWN,
          targetFormat: ContentFormat.HTML,
        };
      }

      // 使用官方方案：Markdown → JSON → HTML
      const json = this.markdownToJSON(markdown);
      if (!json.success) {
        throw new Error(json.error);
      }

      const html = renderToHTMLString({
        content: json.content as JSONContent,
        extensions: this.extensions,
      });

      return {
        success: true,
        content: html,
        originalFormat: ContentFormat.MARKDOWN,
        targetFormat: ContentFormat.HTML,
      };
    } catch (error) {
      return {
        success: false,
        content: "<p></p>",
        originalFormat: ContentFormat.MARKDOWN,
        targetFormat: ContentFormat.HTML,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * HTML 转换为 Markdown
   */
  htmlToMarkdown(html: string): ConvertResult {
    try {
      if (!html || html === "<p></p>") {
        return {
          success: true,
          content: "",
          originalFormat: ContentFormat.HTML,
          targetFormat: ContentFormat.MARKDOWN,
        };
      }

      // 使用官方方案：HTML → JSON → Markdown
      const json = generateJSON(html, this.extensions);
      const markdown = renderToMarkdown({
        content: json,
        extensions: this.extensions,
      });

      return {
        success: true,
        content: markdown,
        originalFormat: ContentFormat.HTML,
        targetFormat: ContentFormat.MARKDOWN,
      };
    } catch (error) {
      return {
        success: false,
        content: "",
        originalFormat: ContentFormat.HTML,
        targetFormat: ContentFormat.MARKDOWN,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Markdown 转换为 JSON
   */
  markdownToJSON(markdown: string): ConvertResult {
    try {
      if (!markdown || typeof markdown !== "string") {
        return {
          success: true,
          content: this.getEmptyDocument(),
          originalFormat: ContentFormat.MARKDOWN,
          targetFormat: ContentFormat.JSON,
        };
      }

      // 简单的 Markdown 到 HTML 转换，然后使用官方 generateJSON
      const simpleHtml = this.simpleMarkdownToHTML(markdown);
      const json = generateJSON(simpleHtml, this.extensions);

      return {
        success: true,
        content: json,
        originalFormat: ContentFormat.MARKDOWN,
        targetFormat: ContentFormat.JSON,
      };
    } catch (error) {
      return {
        success: false,
        content: this.getEmptyDocument(),
        originalFormat: ContentFormat.MARKDOWN,
        targetFormat: ContentFormat.JSON,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * 简单的 Markdown 到 HTML 转换
   * 处理基本的 Markdown 语法
   */
  private simpleMarkdownToHTML(markdown: string): string {
    let html = markdown;

    // 处理标题
    html = html.replace(/^### (.*$)/gim, "<h3>$1</h3>");
    html = html.replace(/^## (.*$)/gim, "<h2>$1</h2>");
    html = html.replace(/^# (.*$)/gim, "<h1>$1</h1>");

    // 处理粗体和斜体
    html = html.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
    html = html.replace(/\*(.*?)\*/g, "<em>$1</em>");

    // 处理代码
    html = html.replace(/`(.*?)`/g, "<code>$1</code>");

    // 处理链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // 处理段落
    html = html.replace(/\n\n/g, "</p><p>");
    html = `<p>${html}</p>`;

    // 清理空段落
    html = html.replace(/<p><\/p>/g, "");

    return html;
  }

  /**
   * JSON 转换为 HTML
   */
  jsonToHTML(json: JSONContent): ConvertResult {
    try {
      const html = renderToHTMLString({
        content: json,
        extensions: this.extensions,
      });

      return {
        success: true,
        content: html,
        originalFormat: ContentFormat.JSON,
        targetFormat: ContentFormat.HTML,
      };
    } catch (error) {
      return {
        success: false,
        content: "<p></p>",
        originalFormat: ContentFormat.JSON,
        targetFormat: ContentFormat.HTML,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * JSON 转换为 Markdown
   */
  jsonToMarkdown(json: JSONContent): ConvertResult {
    try {
      const markdown = renderToMarkdown({
        content: json,
        extensions: this.extensions,
      });

      return {
        success: true,
        content: markdown,
        originalFormat: ContentFormat.JSON,
        targetFormat: ContentFormat.MARKDOWN,
      };
    } catch (error) {
      return {
        success: false,
        content: "",
        originalFormat: ContentFormat.JSON,
        targetFormat: ContentFormat.MARKDOWN,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}
