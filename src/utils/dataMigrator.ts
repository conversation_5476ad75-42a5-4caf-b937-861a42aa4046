/**
 * 数据迁移工具
 * 负责将现有的 Markdown 格式便签迁移到 ProseMirror JSON 格式
 */

import { ContentAdapter, CONTENT_VERSIONS } from "./contentAdapter";
import type { StickyNote } from "../components/types";

/**
 * 迁移进度信息
 */
export interface MigrationProgress {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
  errors: Array<{
    noteId: string;
    error: string;
  }>;
}

/**
 * 迁移选项
 */
export interface MigrationOptions {
  /**
   * 是否在迁移前创建备份
   */
  createBackup?: boolean;

  /**
   * 是否只迁移特定的便签
   */
  noteIds?: string[];

  /**
   * 进度回调函数
   */
  onProgress?: (progress: MigrationProgress) => void;

  /**
   * 是否在失败时停止迁移
   */
  stopOnError?: boolean;

  /**
   * 批处理大小
   */
  batchSize?: number;
}

/**
 * 数据迁移器类
 */
export class DataMigrator {
  private adapter: ContentAdapter;

  constructor() {
    this.adapter = new ContentAdapter();
  }

  /**
   * 检查便签是否需要迁移
   * @param note 便签对象
   * @returns 是否需要迁移
   */
  needsMigration(note: StickyNote): boolean {
    const format = note.contentFormat || "markdown";
    return format === "markdown" && this.hasComplexContent(note.content);
  }

  /**
   * 检查内容是否包含复杂结构（任务列表、表格等）
   * @param content 内容字符串
   * @returns 是否包含复杂结构
   */
  private hasComplexContent(content: string): boolean {
    if (!content) return false;

    // 检查任务列表
    const hasTaskList = /^[\s]*-\s*\[([ x])\]\s*.+$/m.test(content);

    // 检查表格
    const hasTable =
      /\|.*\|/.test(content) && /\|[\s]*:?-+:?[\s]*\|/.test(content);

    // 检查复杂的 HTML 标签
    const hasComplexHTML = /<(?:table|ul|ol|li)[^>]*>/.test(content);

    return hasTaskList || hasTable || hasComplexHTML;
  }

  /**
   * 迁移单个便签
   * @param note 便签对象
   * @returns 迁移结果
   */
  async migrateNote(note: StickyNote): Promise<{
    success: boolean;
    note?: StickyNote;
    error?: string;
  }> {
    try {
      // 检查是否需要迁移
      if (!this.needsMigration(note)) {
        return {
          success: true,
          note: {
            ...note,
            contentFormat: note.contentFormat || "markdown",
            contentVersion: note.contentVersion || CONTENT_VERSIONS.MARKDOWN_V1,
          },
        };
      }

      // 执行内容格式转换
      const conversionResult = this.adapter.convertNoteContent(
        note,
        "prosemirror-json"
      );

      if (conversionResult.success && conversionResult.note) {
        return {
          success: true,
          note: conversionResult.note,
        };
      } else {
        return {
          success: false,
          error: conversionResult.error || "Unknown conversion error",
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `Migration failed: ${error}`,
      };
    }
  }

  /**
   * 批量迁移便签
   * @param notes 便签数组
   * @param options 迁移选项
   * @returns 迁移进度信息
   */
  async migrateNotes(
    notes: StickyNote[],
    options: MigrationOptions = {}
  ): Promise<MigrationProgress> {
    const {
      noteIds,
      onProgress,
      stopOnError = false,
      batchSize = 10,
    } = options;

    // 过滤需要迁移的便签
    let notesToMigrate = notes;
    if (noteIds && noteIds.length > 0) {
      notesToMigrate = notes.filter((note) => noteIds.includes(note.id));
    }

    const progress: MigrationProgress = {
      total: notesToMigrate.length,
      processed: 0,
      succeeded: 0,
      failed: 0,
      errors: [],
    };

    // 分批处理
    for (let i = 0; i < notesToMigrate.length; i += batchSize) {
      const batch = notesToMigrate.slice(i, i + batchSize);

      for (const note of batch) {
        try {
          const result = await this.migrateNote(note);

          progress.processed++;

          if (result.success) {
            progress.succeeded++;
          } else {
            progress.failed++;
            progress.errors.push({
              noteId: note.id,
              error: result.error || "Unknown error",
            });

            if (stopOnError) {
              break;
            }
          }
        } catch (error) {
          progress.processed++;
          progress.failed++;
          progress.errors.push({
            noteId: note.id,
            error: `Unexpected error: ${error}`,
          });

          if (stopOnError) {
            break;
          }
        }

        // 调用进度回调
        if (onProgress) {
          onProgress({ ...progress });
        }
      }

      if (stopOnError && progress.failed > 0) {
        break;
      }

      // 短暂延迟，避免阻塞 UI
      if (i + batchSize < notesToMigrate.length) {
        await new Promise((resolve) => setTimeout(resolve, 10));
      }
    }

    return progress;
  }

  /**
   * 生成迁移报告
   * @param progress 迁移进度信息
   * @returns 迁移报告字符串
   */
  generateMigrationReport(progress: MigrationProgress): string {
    const report = [
      "# 内容格式迁移报告",
      "",
      `**迁移时间**: ${new Date().toLocaleString()}`,
      `**总数**: ${progress.total}`,
      `**成功**: ${progress.succeeded}`,
      `**失败**: ${progress.failed}`,
      `**成功率**: ${
        progress.total > 0
          ? ((progress.succeeded / progress.total) * 100).toFixed(2)
          : 0
      }%`,
      "",
    ];

    if (progress.errors.length > 0) {
      report.push("## 失败详情");
      report.push("");
      progress.errors.forEach((error, index) => {
        report.push(`${index + 1}. **便签ID**: ${error.noteId}`);
        report.push(`   **错误**: ${error.error}`);
        report.push("");
      });
    }

    if (progress.succeeded > 0) {
      report.push("## 迁移说明");
      report.push("");
      report.push("- 成功迁移的便签已转换为 ProseMirror JSON 格式");
      report.push("- 任务列表、表格等复杂内容现在使用原生 TipTap 处理");
      report.push("- 原有的 Markdown 内容已自动转换，保持向后兼容");
      report.push("- 新格式提供更好的编辑体验和数据一致性");
    }

    return report.join("\n");
  }

  /**
   * 分析迁移影响
   * @param notes 便签数组
   * @returns 分析结果
   */
  analyzeMigrationImpact(notes: StickyNote[]): {
    totalNotes: number;
    needsMigration: number;
    hasTaskLists: number;
    hasTables: number;
    hasComplexHTML: number;
    migrationCandidates: string[];
  } {
    const analysis = {
      totalNotes: notes.length,
      needsMigration: 0,
      hasTaskLists: 0,
      hasTables: 0,
      hasComplexHTML: 0,
      migrationCandidates: [] as string[],
    };

    notes.forEach((note) => {
      if (this.needsMigration(note)) {
        analysis.needsMigration++;
        analysis.migrationCandidates.push(note.id);

        // 分析具体内容类型
        const content = note.content;

        if (/^[\s]*-\s*\[([ x])\]\s*.+$/m.test(content)) {
          analysis.hasTaskLists++;
        }

        if (/\|.*\|/.test(content) && /\|[\s]*:?-+:?[\s]*\|/.test(content)) {
          analysis.hasTables++;
        }

        if (/<(?:table|ul|ol|li)[^>]*>/.test(content)) {
          analysis.hasComplexHTML++;
        }
      }
    });

    return analysis;
  }
}

/**
 * 默认迁移器实例
 */
export const defaultMigrator = new DataMigrator();

/**
 * 便捷迁移函数
 */
export const migrationUtils = {
  /**
   * 检查便签是否需要迁移
   */
  needsMigration: (note: StickyNote) => defaultMigrator.needsMigration(note),

  /**
   * 迁移单个便签
   */
  migrateNote: (note: StickyNote) => defaultMigrator.migrateNote(note),

  /**
   * 批量迁移便签
   */
  migrateNotes: (notes: StickyNote[], options?: MigrationOptions) =>
    defaultMigrator.migrateNotes(notes, options),

  /**
   * 分析迁移影响
   */
  analyzeImpact: (notes: StickyNote[]) =>
    defaultMigrator.analyzeMigrationImpact(notes),

  /**
   * 生成迁移报告
   */
  generateReport: (progress: MigrationProgress) =>
    defaultMigrator.generateMigrationReport(progress),
};
