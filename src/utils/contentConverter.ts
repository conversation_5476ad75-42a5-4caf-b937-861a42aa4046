/**
 * 内容转换工具 - 基于 TipTap 官方静态渲染器
 * 支持 ProseMirror JSON ↔ Markdown ↔ HTML 的无损转换
 */

import { generateJSON } from "@tiptap/core";
import type { JSONContent } from "@tiptap/react";
import { renderToMarkdown, renderToHTMLString } from "@tiptap/static-renderer";
import { defaultMarkdownParser } from "prosemirror-markdown";

// 导入所有需要的扩展
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Image from "@tiptap/extension-image";
import { ListKit } from "@tiptap/extension-list";
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";

/**
 * 默认扩展配置
 * 保持与 WysiwygEditor 一致的扩展配置
 */
const getDefaultExtensions = () => [
  StarterKit.configure({
    bulletList: false,
    orderedList: false,
    listItem: false,
    listKeymap: false, // 禁用 StarterKit 中的 listKeymap，改用 ListKit 中的
    italic: {
      HTMLAttributes: {
        class: "italic-text",
      },
    },
    heading: {
      levels: [1, 2, 3, 4, 5, 6],
    },
    codeBlock: false,
    horizontalRule: false,
  }),

  Placeholder.configure({
    placeholder: "开始输入...",
    emptyEditorClass: "is-editor-empty",
  }),

  Image.configure({
    inline: true,
    allowBase64: true,
    HTMLAttributes: {
      class: "editor-image",
    },
  }),

  // ListKit - 包含所有列表扩展
  ListKit.configure({
    // 配置无序列表
    bulletList: {
      HTMLAttributes: {
        class: "bullet-list",
      },
    },
    // 配置有序列表
    orderedList: {
      HTMLAttributes: {
        class: "ordered-list",
      },
    },
    // 配置列表项
    listItem: {},
    // 配置任务列表
    taskList: {
      HTMLAttributes: {
        class: "task-list",
      },
    },
    // 配置任务项
    taskItem: {
      nested: true,
      HTMLAttributes: {
        class: "task-item",
      },
    },
  }),

  // 表格扩展
  Table.configure({
    resizable: true,
    handleWidth: 5,
    cellMinWidth: 25,
    HTMLAttributes: {
      class: "editor-table",
    },
  }),
  TableRow.configure({
    HTMLAttributes: {
      class: "editor-table-row",
    },
  }),
  TableHeader.configure({
    HTMLAttributes: {
      class: "editor-table-header",
    },
  }),
  TableCell.configure({
    HTMLAttributes: {
      class: "editor-table-cell",
    },
  }),
];

/**
 * 内容格式枚举
 */
export const ContentFormat = {
  MARKDOWN: "markdown",
  HTML: "html",
  JSON: "json",
} as const;

export type ContentFormatType =
  (typeof ContentFormat)[keyof typeof ContentFormat];

/**
 * 转换结果接口
 */
export interface ConvertResult {
  success: boolean;
  content: string | JSONContent;
  error?: string;
  originalFormat?: ContentFormatType;
  targetFormat?: ContentFormatType;
}

/**
 * 内容转换器类
 */
export class ContentConverter {
  private extensions: any[];

  constructor(extensions?: any[]) {
    this.extensions = extensions || getDefaultExtensions();
  }

  /**
   * 检测内容格式
   * @param content 内容字符串
   * @returns 检测到的格式
   */
  detectFormat(content: string): ContentFormatType {
    if (!content || typeof content !== "string") {
      return ContentFormat.MARKDOWN;
    }

    const trimmed = content.trim();

    // 检测 JSON 格式
    if (trimmed.startsWith("{") && trimmed.endsWith("}")) {
      try {
        const parsed = JSON.parse(trimmed);
        if (parsed.type === "doc" && parsed.content) {
          return ContentFormat.JSON;
        }
      } catch {
        // 继续检测其他格式
      }
    }

    // 检测 HTML 格式
    if (trimmed.startsWith("<") && trimmed.endsWith(">")) {
      return ContentFormat.HTML;
    }

    // 默认为 Markdown
    return ContentFormat.MARKDOWN;
  }

  /**
   * 简单的 Markdown 到 HTML 转换
   * 支持基本的 Markdown 语法，特别是任务列表和表格
   * @param markdown Markdown 字符串
   * @returns HTML 字符串
   */
  private simpleMarkdownToHTML(markdown: string): string {
    if (!markdown.trim()) return "<p></p>";

    let html = markdown;

    // 🎯 处理表格（Markdown table syntax）
    html = this.convertMarkdownTables(html);

    // 🎯 处理标题（顺序很重要，从最多#开始）
    html = html.replace(/^#{6}\s*(.+)$/gm, "<h6>$1</h6>");
    html = html.replace(/^#{5}\s*(.+)$/gm, "<h5>$1</h5>");
    html = html.replace(/^#{4}\s*(.+)$/gm, "<h4>$1</h4>");
    html = html.replace(/^#{3}\s*(.+)$/gm, "<h3>$1</h3>");
    html = html.replace(/^#{2}\s*(.+)$/gm, "<h2>$1</h2>");
    html = html.replace(/^#{1}\s*(.+)$/gm, "<h1>$1</h1>");

    // 🎯 处理水平分隔线（在其他处理之前）
    html = html.replace(/^[\s]*---+[\s]*$/gm, "<hr>");

    // 🎯 处理代码块（在其他格式处理之前，避免内部被处理）
    html = html.replace(/```([\s\S]*?)```/g, (_match, content) => {
      return `<pre><code>${content.trim()}</code></pre>`;
    });

    // 🎯 处理行内代码（在粗体斜体之前，避免冲突）
    html = html.replace(/`([^`]+)`/g, "<code>$1</code>");

    // 🎯 处理粗体和斜体（顺序很重要！先处理粗体，再处理斜体）
    // 使用更精确的正则，避免贪婪匹配问题
    html = html.replace(
      /\*\*([^*]+(?:\*(?!\*)[^*]*)*)\*\*/g,
      "<strong>$1</strong>"
    );
    html = html.replace(/\*([^*\s][^*]*?[^*\s]|\S)\*/g, "<em>$1</em>");

    // 🎯 处理引用块
    html = html.replace(/^>\s*(.+)$/gm, "<blockquote><p>$1</p></blockquote>");

    // 🎯 处理任务列表（在普通列表之前）
    html = html.replace(
      /^[\s]*-\s*\[([ xX])\]\s*(.+)$/gm,
      (_, checked, text) => {
        const isChecked = checked.toLowerCase() === "x";
        return `<li data-type="taskItem" data-checked="${isChecked}">
        <label>
          <input type="checkbox"${isChecked ? " checked" : ""}>
          <span>${text.trim()}</span>
        </label>
      </li>`;
      }
    );

    // 包装任务列表项
    html = html.replace(
      /(<li data-type="taskItem"[^>]*>[\s\S]*?<\/li>\s*)+/g,
      '<ul data-type="taskList">$&</ul>'
    );

    // 🎯 处理普通无序列表（在任务列表之后）
    html = html.replace(/^[\s]*-\s+(.+)$/gm, "<li>$1</li>");
    html = html.replace(
      /(<li>(?!.*data-type)(?!.*data-list-number)[\s\S]*?<\/li>\s*)+/g,
      "<ul>$&</ul>"
    );

    // 🎯 处理有序列表
    html = html.replace(
      /^[\s]*(\d+)\.\s+(.+)$/gm,
      '<li data-list-number="$1">$2</li>'
    );
    html = html.replace(
      /(<li data-list-number="[^"]*"[^>]*>[\s\S]*?<\/li>\s*)+/g,
      "<ol>$&</ol>"
    );

    // 🎯 处理段落（最后处理，避免影响其他元素）
    const lines = html.split("\n");
    const processedLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line && !line.startsWith("<") && !line.includes("<table>")) {
        processedLines.push(`<p>${line}</p>`);
      } else if (line) {
        processedLines.push(line);
      }
    }

    return processedLines.join("\n") || "<p></p>";
  }

  /**
   * 转换Markdown表格为HTML表格
   * @param markdown 包含表格的Markdown字符串
   * @returns 转换后的HTML字符串
   */
  private convertMarkdownTables(markdown: string): string {
    // 匹配Markdown表格的正则表达式
    const tableRegex = /(?:^|\n)((?:\|[^\n]*\|\n?)+)/gm;

    return markdown.replace(tableRegex, (match, tableContent: string) => {
      const lines = tableContent.trim().split("\n");
      if (lines.length < 2) return match; // 至少需要表头和分隔线

      // 解析表格行
      const rows = lines.map((line: string) =>
        line
          .split("|")
          .slice(1, -1) // 移除首尾空元素
          .map((cell: string) => cell.trim())
      );

      if (rows.length === 0) return match;

      // 检查是否有分隔线（第二行通常是 :--- 格式）
      const hasSeparator =
        rows.length > 1 &&
        rows[1].every((cell: string) => /^:?-+:?$/.test(cell));

      const headerRow = rows[0];
      const dataRows = hasSeparator ? rows.slice(2) : rows.slice(1);

      // 构建HTML表格
      let html = '<table class="editor-table">\n';

      // 表头
      if (headerRow && headerRow.length > 0) {
        html += '  <thead>\n    <tr class="editor-table-row">\n';
        headerRow.forEach((cell: string) => {
          html += `      <th class="editor-table-header">${cell}</th>\n`;
        });
        html += "    </tr>\n  </thead>\n";
      }

      // 表体
      if (dataRows.length > 0) {
        html += "  <tbody>\n";
        dataRows.forEach((row: string[]) => {
          if (row.length > 0) {
            html += '    <tr class="editor-table-row">\n';
            row.forEach((cell: string) => {
              html += `      <td class="editor-table-cell">${cell}</td>\n`;
            });
            html += "    </tr>\n";
          }
        });
        html += "  </tbody>\n";
      }

      html += "</table>";
      return html;
    });
  }

  /**
   * 简单的 HTML 到 Markdown 转换
   * @param html HTML 字符串
   * @returns Markdown 字符串
   */
  private simpleHTMLToMarkdown(html: string): string {
    if (!html || html === "<p></p>") return "";

    let markdown = html;

    // 🎯 处理表格
    markdown = this.convertHTMLTablesToMarkdown(markdown);

    // 🎯 处理水平分隔线
    markdown = markdown.replace(/<hr\s*\/?>/g, "---");

    // 处理任务列表项
    markdown = markdown.replace(
      /<li data-type="taskItem" data-checked="(true|false)"[^>]*>[\s\S]*?<input[^>]*?(checked)?[^>]*>[\s\S]*?<span[^>]*>([\s\S]*?)<\/span>[\s\S]*?<\/li>/g,
      (_, checked, checkedAttr, text) => {
        const isChecked = checked === "true" || checkedAttr;
        const checkbox = isChecked ? "[x]" : "[ ]";
        return `- ${checkbox} ${text.trim()}`;
      }
    );

    // 移除任务列表包装
    markdown = markdown.replace(
      /<ul data-type="taskList">([\s\S]*?)<\/ul>/g,
      "$1"
    );

    // 处理有序列表 - 重建编号（先处理，避免被普通列表规则匹配）
    markdown = markdown.replace(
      /<ol>([\s\S]*?)<\/ol>/g,
      (_match: string, content: string) => {
        let counter = 1;
        return content.replace(
          /<li data-list-number="(\d+)"[^>]*>([\s\S]*?)<\/li>/g,
          (_: string, originalNum: string, text: string) => {
            // 使用原始编号，但确保是连续的
            const num = originalNum || counter;
            counter++;
            return `${num}. ${text.trim()}`;
          }
        );
      }
    );

    // 处理普通列表（在有序列表之后）
    markdown = markdown.replace(/<li>([\s\S]*?)<\/li>/g, "- $1");
    markdown = markdown.replace(/<ul>([\s\S]*?)<\/ul>/g, "$1");

    // 🎯 处理代码块
    markdown = markdown.replace(
      /<pre><code>([\s\S]*?)<\/code><\/pre>/g,
      "```\n$1\n```"
    );

    // 🎯 处理行内代码
    markdown = markdown.replace(/<code>([\s\S]*?)<\/code>/g, "`$1`");

    // 🎯 处理引用块
    markdown = markdown.replace(
      /<blockquote><p>([\s\S]*?)<\/p><\/blockquote>/g,
      "> $1"
    );

    // 处理段落
    markdown = markdown.replace(/<p>([\s\S]*?)<\/p>/g, "$1\n");

    // 处理标题
    for (let i = 1; i <= 6; i++) {
      const regex = new RegExp(`<h${i}[^>]*>([\s\S]*?)<\/h${i}>`, "g");
      markdown = markdown.replace(regex, `${"#".repeat(i)} $1\n`);
    }

    // 🎯 处理粗体和斜体（顺序很重要！先处理strong，再处理em）
    markdown = markdown.replace(/<strong[^>]*>([\s\S]*?)<\/strong>/g, "**$1**");
    markdown = markdown.replace(/<em[^>]*>([\s\S]*?)<\/em>/g, "*$1*");

    // 清理多余的换行
    markdown = markdown.replace(/\n\s*\n/g, "\n\n").trim();

    return markdown;
  }

  /**
   * 转换HTML表格为Markdown表格
   * @param html 包含表格的HTML字符串
   * @returns 转换后的Markdown字符串
   */
  private convertHTMLTablesToMarkdown(html: string): string {
    return html.replace(
      /<table[^>]*>([\s\S]*?)<\/table>/g,
      (_match, tableContent: string) => {
        // 提取表头
        const theadMatch = tableContent.match(
          /<thead[^>]*>([\s\S]*?)<\/thead>/
        );
        const tbodyMatch = tableContent.match(
          /<tbody[^>]*>([\s\S]*?)<\/tbody>/
        );

        let markdownTable = "";
        let columnCount = 0;

        // 处理表头
        if (theadMatch) {
          const headerRows = theadMatch[1].match(/<tr[^>]*>([\s\S]*?)<\/tr>/g);
          if (headerRows && headerRows.length > 0) {
            const headerCells = headerRows[0].match(
              /<th[^>]*>([\s\S]*?)<\/th>/g
            );
            if (headerCells) {
              columnCount = headerCells.length;
              const headerTexts = headerCells.map((cell: string) =>
                cell.replace(/<[^>]*>/g, "").trim()
              );
              markdownTable += "| " + headerTexts.join(" | ") + " |\n";
              markdownTable +=
                "| " + Array(columnCount).fill("---").join(" | ") + " |\n";
            }
          }
        }

        // 处理表体
        if (tbodyMatch) {
          const bodyRows = tbodyMatch[1].match(/<tr[^>]*>([\s\S]*?)<\/tr>/g);
          if (bodyRows) {
            bodyRows.forEach((row: string) => {
              const cells = row.match(/<td[^>]*>([\s\S]*?)<\/td>/g);
              if (cells) {
                const cellTexts = cells.map((cell: string) =>
                  cell.replace(/<[^>]*>/g, "").trim()
                );
                markdownTable += "| " + cellTexts.join(" | ") + " |\n";
              }
            });
          }
        }

        return markdownTable;
      }
    );
  }

  /**
   * 使用混合方法解析 Markdown
   * 优先使用ProseMirror解析器，如果schema不兼容则降级到简单解析器
   * @param markdown Markdown 字符串
   * @returns ProseMirror JSON
   */
  private parseMarkdownWithProseMirror(markdown: string): JSONContent {
    try {
      // 尝试使用ProseMirror的默认解析器
      const proseMirrorDoc = defaultMarkdownParser.parse(markdown);
      const proseMirrorJSON = proseMirrorDoc?.toJSON();

      if (proseMirrorJSON) {
        // 检查是否能转换为TipTap兼容的JSON
        try {
          // 尝试重新解析来验证兼容性 - 如果能成功渲染说明schema兼容
          renderToHTMLString({
            content: proseMirrorJSON,
            extensions: this.extensions,
          });

          // 如果成功渲染，则返回JSON
          return proseMirrorJSON;
        } catch (renderError) {
          console.warn(
            "ProseMirror JSON incompatible with TipTap extensions, using hybrid approach"
          );
          // 降级到HTML转换方法
          throw new Error("Schema incompatible");
        }
      }

      throw new Error("ProseMirror parser returned null");
    } catch (error) {
      console.warn("Using fallback HTML-based parsing:", error);
      // 降级到简单HTML转换
      const html = this.simpleMarkdownToHTML(markdown);
      return generateJSON(html, this.extensions);
    }
  }

  /**
   * Markdown 转换为 ProseMirror JSON
   * @param markdown Markdown 字符串
   * @returns 转换结果
   */
  markdownToJSON(markdown: string): ConvertResult {
    try {
      if (!markdown || typeof markdown !== "string") {
        return {
          success: true,
          content: this.getEmptyDocument(),
          originalFormat: ContentFormat.MARKDOWN,
          targetFormat: ContentFormat.JSON,
        };
      }

      // 🎯 优先使用ProseMirror官方Markdown解析器
      const json = this.parseMarkdownWithProseMirror(markdown);

      return {
        success: true,
        content: json,
        originalFormat: ContentFormat.MARKDOWN,
        targetFormat: ContentFormat.JSON,
      };
    } catch (error) {
      console.error("Markdown to JSON conversion failed:", error);
      return {
        success: false,
        content: this.getEmptyDocument(),
        error: `Markdown to JSON conversion failed: ${error}`,
        originalFormat: ContentFormat.MARKDOWN,
        targetFormat: ContentFormat.JSON,
      };
    }
  }

  /**
   * ProseMirror JSON 转换为 Markdown
   * @param json ProseMirror JSON 对象
   * @returns 转换结果
   */
  jsonToMarkdown(json: JSONContent): ConvertResult {
    try {
      if (!json || typeof json !== "object") {
        return {
          success: true,
          content: "",
          originalFormat: ContentFormat.JSON,
          targetFormat: ContentFormat.MARKDOWN,
        };
      }

      // 🎯 使用TipTap官方静态渲染器
      const markdown = renderToMarkdown({
        content: json,
        extensions: this.extensions,
      });

      return {
        success: true,
        content: markdown,
        originalFormat: ContentFormat.JSON,
        targetFormat: ContentFormat.MARKDOWN,
      };
    } catch (error) {
      console.error("JSON to Markdown conversion failed:", error);
      return {
        success: false,
        content: this.convertJSONToMarkdown(json), // 降级到手动转换
        error: `JSON to Markdown conversion failed: ${error}`,
        originalFormat: ContentFormat.JSON,
        targetFormat: ContentFormat.MARKDOWN,
      };
    }
  }

  /**
   * 递归转换 JSON 内容为 Markdown
   * @param node JSON 节点
   * @returns Markdown 字符串
   */
  private convertJSONToMarkdown(node: JSONContent): string {
    if (!node || !node.type) return "";

    let result = "";

    switch (node.type) {
      case "doc":
        if (node.content) {
          result = node.content
            .map((child) => this.convertJSONToMarkdown(child))
            .join("\n");
        }
        break;

      case "paragraph":
        if (node.content) {
          result =
            node.content
              .map((child) => this.convertJSONToMarkdown(child))
              .join("") + "\n";
        } else {
          result = "\n";
        }
        break;

      case "text":
        result = node.text || "";
        break;

      case "taskList":
        if (node.content) {
          result = node.content
            .map((child) => this.convertJSONToMarkdown(child))
            .join("");
        }
        break;

      case "taskItem":
        const checked = node.attrs?.checked || false;
        const checkbox = checked ? "[x]" : "[ ]";
        if (node.content) {
          const content = node.content
            .map((child) => this.convertJSONToMarkdown(child))
            .join("")
            .trim();
          result = `- ${checkbox} ${content}\n`;
        }
        break;

      case "bulletList":
        if (node.content) {
          result = node.content
            .map((child) => this.convertJSONToMarkdown(child))
            .join("");
        }
        break;

      case "orderedList":
        if (node.content) {
          result = node.content
            .map((child, index) => {
              const content = this.convertJSONToMarkdown(child);
              return content.replace(/^- /, `${index + 1}. `);
            })
            .join("");
        }
        break;

      case "listItem":
        if (node.content) {
          const content = node.content
            .map((child) => this.convertJSONToMarkdown(child))
            .join("")
            .trim();
          result = `- ${content}\n`;
        }
        break;

      case "heading":
        const level = node.attrs?.level || 1;
        if (node.content) {
          const content = node.content
            .map((child) => this.convertJSONToMarkdown(child))
            .join("");
          result = `${"#".repeat(level)} ${content}\n`;
        }
        break;

      default:
        // 处理其他节点类型
        if (node.content) {
          result = node.content
            .map((child) => this.convertJSONToMarkdown(child))
            .join("");
        }
        break;
    }

    return result;
  }

  /**
   * HTML 转换为 Markdown
   * @param html HTML 字符串
   * @returns 转换结果
   */
  htmlToMarkdown(html: string): ConvertResult {
    try {
      if (!html || html === "<p></p>")
        return {
          success: true,
          content: "",
          originalFormat: ContentFormat.HTML,
          targetFormat: ContentFormat.MARKDOWN,
        };

      // 🎯 使用TipTap官方方案：HTML → JSON → Markdown
      const json = generateJSON(html, this.extensions);
      const markdown = renderToMarkdown({
        content: json,
        extensions: this.extensions,
      });

      return {
        success: true,
        content: markdown,
        originalFormat: ContentFormat.HTML,
        targetFormat: ContentFormat.MARKDOWN,
      };
    } catch (error) {
      console.error("HTML to Markdown conversion failed:", error);
      return {
        success: false,
        content: this.simpleHTMLToMarkdown(html), // 降级到简单转换
        error: `HTML to Markdown conversion failed: ${error}`,
        originalFormat: ContentFormat.HTML,
        targetFormat: ContentFormat.MARKDOWN,
      };
    }
  }

  /**
   * Markdown 转换为 HTML
   * @param markdown Markdown 字符串
   * @returns 转换结果
   */
  markdownToHTML(markdown: string): ConvertResult {
    try {
      if (!markdown.trim())
        return {
          success: true,
          content: "<p></p>",
          originalFormat: ContentFormat.MARKDOWN,
          targetFormat: ContentFormat.HTML,
        };

      // 🎯 先用简单转换将Markdown转为HTML，再用官方渲染器优化
      const simpleHtml = this.simpleMarkdownToHTML(markdown);
      const json = generateJSON(simpleHtml, this.extensions);
      const html = renderToHTMLString({
        content: json,
        extensions: this.extensions,
      });

      return {
        success: true,
        content: html,
        originalFormat: ContentFormat.MARKDOWN,
        targetFormat: ContentFormat.HTML,
      };
    } catch (error) {
      console.error("Markdown to HTML conversion failed:", error);
      return {
        success: false,
        content: this.simpleMarkdownToHTML(markdown), // 降级到简单转换
        error: `Markdown to HTML conversion failed: ${error}`,
        originalFormat: ContentFormat.MARKDOWN,
        targetFormat: ContentFormat.HTML,
      };
    }
  }

  /**
   * 获取空文档的 JSON 结构
   * @returns 空的 ProseMirror 文档
   */
  private getEmptyDocument(): JSONContent {
    return {
      type: "doc",
      content: [
        {
          type: "paragraph",
        },
      ],
    };
  }
}

/**
 * 默认转换器实例
 */
export const defaultConverter = new ContentConverter();

/**
 * 便捷转换函数
 */
export const convertContent = {
  /**
   * Markdown 转 JSON
   */
  markdownToJSON: (markdown: string) =>
    defaultConverter.markdownToJSON(markdown),

  /**
   * JSON 转 Markdown
   */
  jsonToMarkdown: (json: JSONContent) => defaultConverter.jsonToMarkdown(json),

  /**
   * HTML 转 Markdown
   */
  htmlToMarkdown: (html: string) => defaultConverter.htmlToMarkdown(html),

  /**
   * Markdown 转 HTML
   */
  markdownToHTML: (markdown: string) =>
    defaultConverter.markdownToHTML(markdown),
};
