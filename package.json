{"name": "infinity-notes", "private": false, "version": "1.0.0", "type": "module", "description": "无限画布思维整理工具 - 在无限画布上创建、连接和整理想法，支持AI智能汇总功能", "keywords": ["infinite-canvas", "sticky-notes", "mind-mapping", "ai-summary", "react", "typescript", "vite", "IndexedDB"], "homepage": "./", "repository": {"type": "git", "url": "git+https://github.com/your-username/infinity-notes.git"}, "bugs": {"url": "https://github.com/your-username/infinity-notes/issues"}, "license": "MIT", "author": {"name": "无限便签项目贡献者", "url": "https://github.com/your-username/infinity-notes/graphs/contributors"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "node scripts/inject-version.js && tsc -b && vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "serve": "npm run build && npm run preview", "test:build": "npm run build && echo '构建完成，请运行 npm run preview 进行测试'", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "generate-icons": "node scripts/generate-icons.js", "electron:build": "npm run generate-icons && node scripts/inject-version.js && npm run build && electron-builder", "dist": "npm run generate-icons && node scripts/inject-version.js && ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ npm run build && electron-builder --publish=never", "dist:mac": "npm run generate-icons && node scripts/inject-version.js && ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ npm run build && electron-builder --mac", "dist:win": "node scripts/inject-version.js && ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ npm run build && electron-builder --win", "dist:linux": "ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ node scripts/inject-version.js && npm run build && ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ electron-builder --linux", "dist:linux:mirror": "./scripts/build-linux.sh", "package:linux": "./scripts/package-linux.sh", "dist:small": "npm run generate-icons && node scripts/inject-version.js && npm run build && electron-builder --publish=never --config.compression=maximum", "analyze-bundle": "node scripts/simple-image-optimize.js && npm run build && npx vite-bundle-analyzer dist/assets --mode=static", "deploy:web": "./scripts/deploy-web.sh", "build:web": "npm run build", "preview:web": "npm run build && npm run preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@eslint/js": "^9.29.0", "@icon-park/react": "^1.4.2", "@tiptap/extension-image": "^3.0.9", "@tiptap/extension-placeholder": "^3.0.9", "@tiptap/extension-table": "^3.0.9", "@tiptap/extension-table-cell": "^3.0.9", "@tiptap/extension-table-header": "^3.0.9", "@tiptap/extension-table-row": "^3.0.9", "@tiptap/extension-task-item": "^3.1.0", "@tiptap/extension-task-list": "^3.1.0", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "@tiptap/static-renderer": "^3.1.0", "antd": "^5.25.3", "electron-store": "^8.2.0", "leader-line": "^1.0.8", "lodash": "^4.17.21", "node-fetch": "^3.3.2", "prosemirror-markdown": "^1.13.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/lodash": "^4.17.17", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.2.0", "electron": "^37.2.1", "electron-builder": "^26.0.12", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "png2icons": "^2.0.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.4", "wait-on": "^8.0.3"}, "build": {"appId": "com.duobaobox.infinity-notes", "productName": "Infinity Notes", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "public/icon.png", "public/icon.icns", "public/icon.ico", "!dist/**/*.map", "!dist/**/*.md", "!dist/**/stats.html", "!node_modules/**/*"], "compression": "maximum", "removePackageScripts": true, "asar": true, "asarUnpack": [], "electronVersion": "37.2.1", "mac": {"icon": "public/icon.icns", "category": "public.app-category.productivity", "minimumSystemVersion": "10.15.0", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "asarUnpack": ["**/*.node"]}, "win": {"icon": "public/icon.ico", "target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "asarUnpack": ["**/*.node"]}, "linux": {"icon": "public/icon.png", "category": "Office", "target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}